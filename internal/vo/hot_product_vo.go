package vo

// HotProductResponse 热门产品响应视图对象
type HotProductResponse struct {
	ID          uint   `json:"id" example:"1"`
	Name        string `json:"name" example:"京东E卡"`
	ImageURL1   string `json:"image_url1" example:"https://example.com/image.jpg"`
	DiscountTip string `json:"discount_tip" example:"9.5折起"`
}

// HotProductListResponse 热门产品列表响应视图对象
type HotProductListResponse struct {
	List []HotProductResponse `json:"list"`
}

// HotProductCreateRequest 创建热门产品请求视图对象
type HotProductCreateRequest struct {
	ProductID uint `json:"product_id" binding:"required" example:"1"`
	Sort      int  `json:"sort" binding:"required" example:"1"`
}

// HotProductUpdateRequest 更新热门产品请求视图对象
type HotProductUpdateRequest struct {
	Sort int `json:"sort" binding:"required" example:"1"`
}
