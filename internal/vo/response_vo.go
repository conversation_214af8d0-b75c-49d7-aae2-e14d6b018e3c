package vo

// APIResponse 通用API响应结构，使用泛型T表示数据类型
// swagger:response
// 用于替代SuccessResponse和ErrorResponse，统一API响应格式
type APIResponse[T any] struct {
	Code    int    `json:"code" example:"200"`     // 业务状态码
	Message string `json:"message" example:"操作成功"` // 响应消息
	Data    T      `json:"data"`                   // 响应数据，泛型参数
}

// SuccessAPIResponse 成功响应的APIResponse特化，用于Swagger文档
type SuccessAPIResponse struct {
	Code    int         `json:"code" example:"200"`     // 业务状态码
	Message string      `json:"message" example:"操作成功"` // 响应消息
	Data    interface{} `json:"data"`                   // 响应数据
}

// ErrorAPIResponse 错误响应的APIResponse特化，用于Swagger文档
type ErrorAPIResponse struct {
	Code    int         `json:"code" example:"400"`       // 业务状态码
	Message string      `json:"message" example:"请求参数错误"` // 错误消息
	Data    interface{} `json:"data"`                     // 响应数据，通常为null
}

// PaginatedList 通用分页列表响应结构，使用泛型T表示列表项类型
type PaginatedList[T any] struct {
	Total    int64 `json:"total" example:"100"`    // 总记录数
	Page     int   `json:"page" example:"1"`       // 当前页码
	PageSize int   `json:"page_size" example:"10"` // 每页条数
	List     []T   `json:"list"`                   // 数据列表
}

// NewPaginatedList 创建通用分页列表响应
func NewPaginatedList[T any](list []T, total int64, page, pageSize int) PaginatedList[T] {
	return PaginatedList[T]{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		List:     list,
	}
}