package main

import (
	"fmt"
	"log"

	"go.uber.org/zap"
)

// 简单示例脚本：展示如何使用脚本依赖注入系统
func main() {
	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		log.Fatalf("初始化脚本基础失败: %v", err)
	}
	defer scriptBase.Close()

	scriptBase.Info("简单脚本开始执行")

	// 获取脚本服务
	scriptService := scriptBase.GetScriptService()

	// 示例1: 获取配置信息
	config := scriptService.GetConfig()
	scriptBase.Info("应用配置",
		zap.String("app_name", config.App.Name),
		zap.String("app_env", config.App.Env),
		zap.Int("app_port", config.App.Port))

	// 示例2: 获取数据库连接
	db := scriptService.GetDB()
	scriptBase.Info("数据库连接已获取", zap.String("db_type", fmt.Sprintf("%T", db)))

	// 示例3: 获取Redis客户端
	redis := scriptService.GetRedis()
	scriptBase.Info("Redis客户端已获取", zap.String("redis_type", fmt.Sprintf("%T", redis)))

	// 示例4: 获取用户服务
	userService := scriptService.GetUserService()
	scriptBase.Info("用户服务已获取", zap.String("service_type", fmt.Sprintf("%T", userService)))

	// 示例5: 获取上传服务
	uploadService := scriptService.GetUploadService()
	scriptBase.Info("上传服务已获取", zap.String("service_type", fmt.Sprintf("%T", uploadService)))

	// 示例6: 获取验证码服务
	codeService := scriptService.GetCodeService()
	scriptBase.Info("验证码服务已获取", zap.String("service_type", fmt.Sprintf("%T", codeService)))

	// 示例7: 获取基础设施服务
	jwtService := scriptService.GetJWTService()
	smsService := scriptService.GetSMSService()
	payService := scriptService.GetPayService()

	scriptBase.Info("基础设施服务已获取",
		zap.String("jwt_service", fmt.Sprintf("%T", jwtService)),
		zap.String("sms_service", fmt.Sprintf("%T", smsService)),
		zap.String("pay_service", fmt.Sprintf("%T", payService)))

	scriptBase.Info("简单脚本执行完成")

	// 在这里可以调用具体的业务方法
	// 例如：
	// user, err := userService.GetUserByID(1)
	// if err != nil {
	//     scriptBase.Error("获取用户失败", zap.Error(err))
	//     return
	// }
	// scriptBase.Info("获取用户成功", zap.String("nickname", user.Nickname))
}
