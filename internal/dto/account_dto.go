package dto

// AddAlipayAccountRequest 添加支付宝账户请求DTO
type AddAlipayAccountRequest struct {
	AlipayAccount string `json:"alipay_account" binding:"required,min=1,max=100" example:"***********" label:"支付宝账号"`
}

// AddBankCardRequest 添加银行卡请求DTO
type AddBankCardRequest struct {
	BankAccount string `json:"bank_account" binding:"required,min=1,max=30" example:"6222021234567890123" label:"银行账户"`
	BankName    string `json:"bank_name" binding:"required,min=1,max=100" example:"中国工商银行" label:"开户银行"`
}

// DeleteAlipayAccountRequest 删除支付宝账户请求DTO
type DeleteAlipayAccountRequest struct {
	ID            uint   `uri:"id" binding:"required,min=1" example:"1" label:"支付宝账户ID"`
	TradePassword string `json:"trade_password" binding:"required,min=6,max=20" example:"123456" label:"交易密码"`
}

// DeleteBankCardRequest 删除银行卡请求DTO
type DeleteBankCardRequest struct {
	ID            uint   `uri:"id" binding:"required,min=1" example:"1" label:"银行卡ID"`
	TradePassword string `json:"trade_password" binding:"required,min=6,max=20" example:"123456" label:"交易密码"`
}
