package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterAuthRoutes 注册认证相关路由
func RegisterAuthRoutes(
	router *gin.RouterGroup,
	authController *api.AuthController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 认证路由组
	authGroup := router.Group("/auth")
	{
		// 验证码登录路由
		authGroup.POST("/login-code", middleware.WithValidation(authController.LoginCode))
		// 发送短信验证码
		authGroup.POST("/sms/code", middleware.WithValidation(authController.SendSMSCode))

	}
}
