package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterAccountRoutes 注册账户相关路由
func RegisterAccountRoutes(
	router *gin.RouterGroup,
	accountController *api.AccountController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 账户路由组 - 需要认证
	accountGroup := router.Group("/accounts")
	accountGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 支付宝账户相关路由
		alipayGroup := accountGroup.Group("/alipay")
		{
			// 添加支付宝账户
			alipayGroup.POST("", middleware.WithValidation(accountController.AddAlipayAccount))
			// 获取所有支付宝账户
			alipayGroup.GET("", accountController.GetAllAlipayAccounts)
			// 删除支付宝账户
			alipayGroup.DELETE("/:id", middleware.WithValidation(accountController.DeleteAlipayAccount))
		}

		// 银行卡相关路由
		bankCardGroup := accountGroup.Group("/bank-card")
		{
			// 添加银行卡
			bankCardGroup.POST("", middleware.WithValidation(accountController.AddBankCard))
			// 获取所有银行卡
			bankCardGroup.GET("", accountController.GetAllBankCards)
			// 删除银行卡
			bankCardGroup.DELETE("/:id", middleware.WithValidation(accountController.DeleteBankCard))
		}
	}
}
