package models

import (
	"recycle-server/internal/enum"
)

// SystemMessage 系统消息模型
type SystemMessage struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Title       string `json:"title" gorm:"size:255;not null;comment:消息标题"`
	Description string `json:"description" gorm:"type:text;comment:消息描述"`
	SystemType  enum.SystemMessageType `json:"system_type" gorm:"type:tinyint(1);default:1;index;comment:消息类型 1:系统通知 2:活动消息 3:维护公告"`

	Base
}

// TableName 指定表名
func (SystemMessage) TableName() string {
	return "system_message"
}
