package vo

// ProductCategoryResponse 产品分类响应视图对象
type ProductCategoryResponse struct {
	ID       uint                     `json:"id" example:"1"`
	Name     string                   `json:"name" example:"电商卡"`
	Children []ProductSummaryResponse `json:"children"`
}

// ProductSummaryResponse 产品摘要响应视图对象
type ProductSummaryResponse struct {
	ID        uint   `json:"id" example:"1"`
	Name      string `json:"name" example:"京东E卡"`
	ImageURL1 string `json:"image_url1" example:"https://example.com/image.jpg"`
}

// ProductSearchResponse 产品搜索响应视图对象
type ProductSearchResponse struct {
	ID          uint   `json:"id" example:"1"`
	Name        string `json:"name" example:"京东E卡"`
	ImageURL1   string `json:"image_url1" example:"https://example.com/image.jpg"`
	DiscountTip string `json:"discount_tip" example:"9.5折起"`
}

// ProductSearchListResponse 产品搜索列表响应视图对象
type ProductSearchListResponse struct {
	List []ProductSearchResponse `json:"list"`
}

// FaceValueResponse 面值响应视图对象
type FaceValueResponse struct {
	ID        uint    `json:"id" example:"1"`
	Name      string  `json:"name" example:"100元"`
	Value     float64 `json:"value" example:"100.00"`
	Status    bool    `json:"status" example:"true"`
	MyMaxRate string  `json:"my_max_rate" example:"95.5"`
	MyMinRate string  `json:"my_min_rate" example:"94.0"`
	MyRate    string  `json:"my_rate" example:"94.8"`
}

// ProductDetailResponse 产品详情响应视图对象
type ProductDetailResponse struct {
	ID           uint                `json:"id" example:"1"`
	Name         string              `json:"name" example:"京东E卡"`
	CategoryID   uint                `json:"category_id" example:"1"`
	ImageURL1    string              `json:"image_url1" example:"https://example.com/image1.jpg"`
	ImageURL2    string              `json:"image_url2" example:"https://example.com/image2.jpg"`
	CardNoRule   string              `json:"card_no_rule" example:"16位数字"`
	CardPwdRule  string              `json:"card_pwd_rule" example:"8位数字"`
	WriteOffDesc string              `json:"write_off_desc" example:"核销说明"`
	Demo         string              `json:"demo" example:"演示内容"`
	RuleTip      string              `json:"rule_tip" example:"规则提示"`
	RuleDesc     string              `json:"rule_desc" example:"详细规则描述"`
	DiscountTip  string              `json:"discount_tip" example:"9.5折起"`
	Category     CategoryResponse    `json:"category"`
	FaceValues   []FaceValueResponse `json:"face_values"`
}

// CategoryResponse 分类响应视图对象
type CategoryResponse struct {
	ID   uint   `json:"id" example:"1"`
	Name string `json:"name" example:"电商卡"`
}
