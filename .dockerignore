# Git
.git
.gitignore

# Documentation
README.md
docs/

# Development files
.env
.env.local
.env.development
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Build artifacts
recycle-server
*.exe
*.dll
*.so
*.dylib

# Test files
*_test.go
test/
coverage.out

# Logs
logs/
*.log

# Temporary files
tmp/
temp/

# Node modules (if any)
node_modules/

# Docker files
Dockerfile*
docker-compose*.yml

# Scripts (except necessary ones)
scripts/

# Certificates (should be mounted)
cert/
*.pem
*.key
*.crt
