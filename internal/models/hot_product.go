package models

// HotProduct 热门产品模型
type HotProduct struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	ProductID uint `json:"product_id" gorm:"not null;default:0;index;comment:产品ID"`
	Sort      int  `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序字段，数值越小越靠前"`

	// 关联产品
	Product Product `json:"product" gorm:"foreignKey:ProductID;references:ID"`

	Base
}

// TableName 指定表名
func (HotProduct) TableName() string {
	return "hot_product"
}
