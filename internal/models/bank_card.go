package models

// BankCard 银行卡模型
type BankCard struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	UserID      uint   `json:"user_id" gorm:"not null;index;default:0;comment:用户ID"`
	BankAccount string `json:"bank_account" gorm:"size:30;not null;default:'';comment:银行账户"`
	BankName    string `json:"bank_name" gorm:"size:100;not null;default:'';comment:开户银行"`

	Base

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 指定表名
func (BankCard) TableName() string {
	return "bank_card"
}
