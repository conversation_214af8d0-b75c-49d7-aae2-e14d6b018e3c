package enum

// SystemMessageType 系统消息类型
type SystemMessageType int

const (
	SystemMessageTypeNotification SystemMessageType = 1 // 系统通知
	SystemMessageTypeActivity     SystemMessageType = 2 // 活动消息
	SystemMessageTypeMaintenance  SystemMessageType = 3 // 维护公告
)

// String 返回系统消息类型的字符串表示
func (s SystemMessageType) String() string {
	switch s {
	case SystemMessageTypeNotification:
		return "系统通知"
	case SystemMessageTypeActivity:
		return "活动消息"
	case SystemMessageTypeMaintenance:
		return "维护公告"
	default:
		return "未知类型"
	}
}
