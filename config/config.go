package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构体
type Config struct {
	App           AppConfig
	Database      DatabaseConfig
	JWT           JWTConfig
	Log           LogConfig
	Swagger       SwaggerConfig
	Redis         RedisConfig
	WeChat        WeChatConfig
	AliPay        AliPayConfig
	SMS           SMSConfig
	BackendDomain string // 后端域名
}

// AppConfig 应用基本配置
type AppConfig struct {
	Name  string
	Env   string
	Debug bool
	Host  string
	Port  int
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Connection                string
	Host                      string
	Port                      int
	Database                  string
	Username                  string
	Password                  string
	Charset                   string        // 字符集
	ParseTime                 bool          // 是否解析时间
	TimeZone                  string        // 时区
	DefaultStringSize         uint          // 字符串默认长度
	DisableDatetimePrecision  bool          // 禁用datetime精度
	SkipInitializeWithVersion bool          // 是否根据MySQL版本自动配置
	AutoMigrate               bool          // 是否自动迁移表结构
	SlowSQL                   time.Duration // 慢SQL阈值
	LogLevel                  string        // 日志级别
	IgnoreRecordNotFoundError bool          // 是否忽略记录未找到错误
	MaxIdleConn               int           // 最大空闲连接数
	MaxOpenConn               int           // 最大连接数
	ConnMaxLifetime           time.Duration // 连接最大生命周期
	ConnMaxIdleTime           time.Duration // 最大空闲时间
	// GORM配置
	GormSkipDefaultTx   bool   // 是否跳过默认事务
	GormTablePrefix     string // 表前缀
	GormSingularTable   bool   // 是否使用单数表名
	GormCoverLogger     bool   // 是否覆盖默认logger
	GormPrepareStmt     bool   // 是否使用预处理语句
	GormCloseForeignKey bool   // 是否关闭外键约束
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string
	TTL    int
}

// LogConfig 日志配置
type LogConfig struct {
	Level   string
	Channel string
}

// SwaggerConfig Swagger文档配置
type SwaggerConfig struct {
	Enable bool
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string
	Port     int
	Password string
	DB       int
}

// WeChatConfig 微信配置
type WeChatConfig struct {
	// 微信支付配置
	PayAppID         string // 微信支付AppID
	PayMchID         string // 微信支付商户号
	PayPlatformCerts string // 微信支付平台证书路径
	PayPrivateKey    string // 微信支付私钥路径
	PaySecretKey     string // 微信支付API密钥
	PayCertificate   string // 微信支付证书路径
	PayNotifyURL     string // 微信支付回调通知地址
}

// AliPayConfig 支付宝支付配置
type AliPayConfig struct {
	AppID             string // 支付宝应用ID
	AppSecretCertPath string // 支付宝应用私钥文件路径
	AppPublicCertPath string // 应用公钥证书路径
	PublicCertPath    string // 支付宝公钥证书路径
	RootCertPath      string // 支付宝根证书路径
	NotifyURL         string // 支付宝回调通知地址
	IsProduction      bool   // 是否是生产环境
}

// SMSConfig 短信服务配置
type SMSConfig struct {
	AccessKeyID       string // 阿里云AccessKeyID
	AccessKeySecret   string // 阿里云AccessKeySecret
	Endpoint          string // 短信服务端点
	SignName          string // 短信签名
	LoginTemplateCode string // 登录验证码模板
}

// LoadConfig 从.env文件和环境变量加载配置
func LoadConfig() (*Config, error) {
	// 使用Viper读取配置
	v := viper.New()

	// 设置环境变量前缀和自动读取环境变量
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 尝试读取.env文件（如果存在）
	envFile, err := os.ReadFile(".env")
	if err == nil {
		// 转换.env为YAML格式
		yamlContent := parseEnvToYaml(string(envFile))

		// 创建临时YAML文件
		tmpFile, err := os.CreateTemp("", "config-*.yaml")
		if err != nil {
			return nil, fmt.Errorf("无法创建临时YAML文件: %w", err)
		}
		defer os.Remove(tmpFile.Name())

		if _, err := tmpFile.WriteString(yamlContent); err != nil {
			return nil, fmt.Errorf("无法写入临时YAML文件: %w", err)
		}
		tmpFile.Close()

		// 读取.env文件内容
		v.SetConfigFile(tmpFile.Name())
		v.SetConfigType("yaml")
		if err := v.ReadInConfig(); err != nil {
			return nil, fmt.Errorf("无法读取配置文件: %w", err)
		}
	}

	config := &Config{}

	// 应用配置
	config.App.Name = v.GetString("app.name")
	config.App.Env = v.GetString("app.env")
	config.App.Debug = v.GetBool("app.debug")
	config.App.Host = v.GetString("app.host")
	config.App.Port = v.GetInt("app.port")

	// 数据库配置
	config.Database.Connection = v.GetString("db.connection")
	config.Database.Host = v.GetString("db.host")
	config.Database.Port = v.GetInt("db.port")
	config.Database.Database = v.GetString("db.database")
	config.Database.Username = v.GetString("db.username")
	config.Database.Password = v.GetString("db.password")

	// 数据库高级配置
	config.Database.Charset = v.GetString("db.charset")
	config.Database.ParseTime = v.GetBool("db.parse_time")
	config.Database.TimeZone = v.GetString("db.time_zone")
	config.Database.DefaultStringSize = uint(v.GetInt("db.default_string_size"))
	config.Database.DisableDatetimePrecision = v.GetBool("db.disable_datetime_precision")
	config.Database.SkipInitializeWithVersion = v.GetBool("db.skip_initialize_with_version")
	config.Database.AutoMigrate = v.GetBool("db.auto_migrate")
	config.Database.SlowSQL = time.Duration(v.GetInt("db.slow_sql")) * time.Millisecond
	config.Database.LogLevel = v.GetString("db.log_level")
	config.Database.IgnoreRecordNotFoundError = v.GetBool("db.ignore_record_not_found_error")
	config.Database.MaxIdleConn = v.GetInt("db.max_idle_conn")
	config.Database.MaxOpenConn = v.GetInt("db.max_open_conn")
	config.Database.ConnMaxLifetime = time.Duration(v.GetInt("db.conn_max_lifetime")) * time.Hour
	config.Database.ConnMaxIdleTime = time.Duration(v.GetInt("db.conn_max_idle_time")) * time.Hour

	// GORM配置
	config.Database.GormSkipDefaultTx = v.GetBool("db.gorm.skip_default_tx")
	config.Database.GormTablePrefix = v.GetString("db.gorm.table_prefix")
	config.Database.GormSingularTable = v.GetBool("db.gorm.singular_table")
	config.Database.GormCoverLogger = v.GetBool("db.gorm.cover_logger")
	config.Database.GormPrepareStmt = v.GetBool("db.gorm.prepare_stmt")
	config.Database.GormCloseForeignKey = v.GetBool("db.gorm.close_foreign_key")

	// JWT配置
	config.JWT.Secret = v.GetString("jwt.secret")
	config.JWT.TTL = v.GetInt("jwt.ttl")

	// 日志配置
	config.Log.Level = v.GetString("log.level")
	config.Log.Channel = v.GetString("log.channel")

	// Swagger配置
	config.Swagger.Enable = v.GetBool("swagger.enable")

	// Redis配置
	config.Redis.Host = v.GetString("redis.host")
	config.Redis.Port = v.GetInt("redis.port")
	config.Redis.Password = v.GetString("redis.password")
	config.Redis.DB = v.GetInt("redis.db")

	// 微信支付配置
	config.WeChat.PayAppID = v.GetString("wechat.pay_app_id")
	config.WeChat.PayMchID = v.GetString("wechat.pay_mch_id")
	config.WeChat.PayPlatformCerts = v.GetString("wechat.pay_platform_certs")
	config.WeChat.PayPrivateKey = v.GetString("wechat.pay_private_key")
	config.WeChat.PaySecretKey = v.GetString("wechat.pay_secret_key")
	config.WeChat.PayCertificate = v.GetString("wechat.pay_certificate")
	config.WeChat.PayNotifyURL = v.GetString("wechat.pay_notify_url")

	// 支付宝配置
	config.AliPay = AliPayConfig{
		AppID:             v.GetString("alipay.app_id"),
		AppSecretCertPath: v.GetString("alipay.app_secret_cert"),
		AppPublicCertPath: v.GetString("alipay.app_public_cert_path"),
		PublicCertPath:    v.GetString("alipay.public_cert_path"),
		RootCertPath:      v.GetString("alipay.root_cert_path"),
		NotifyURL:         v.GetString("alipay.notify_url"),
		IsProduction:      v.GetBool("alipay.is_production"),
	}

	// SMS配置
	config.SMS = SMSConfig{
		AccessKeyID:       v.GetString("sms.access_key_id"),
		AccessKeySecret:   v.GetString("sms.access_key_secret"),
		Endpoint:          v.GetString("sms.endpoint"),
		SignName:          v.GetString("sms.sign_name"),
		LoginTemplateCode: v.GetString("sms.login_template_code"),
	}

	// 后端域名配置
	config.BackendDomain = v.GetString("backend.domain")

	return config, nil
}

// 将.env格式解析为YAML格式
func parseEnvToYaml(envContent string) string {
	var yamlParts []string
	lines := strings.Split(envContent, "\n")

	var currentSection string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		// 根据命名约定确定配置段
		var section, subKey string
		if strings.HasPrefix(key, "APP_") {
			section = "app"
			subKey = strings.ToLower(strings.TrimPrefix(key, "APP_"))
		} else if strings.HasPrefix(key, "DB_GORM_") {
			section = "db.gorm"
			subKey = strings.ToLower(strings.TrimPrefix(key, "DB_GORM_"))
		} else if strings.HasPrefix(key, "DB_") {
			section = "db"
			subKey = strings.ToLower(strings.TrimPrefix(key, "DB_"))
		} else if strings.HasPrefix(key, "JWT_") {
			section = "jwt"
			subKey = strings.ToLower(strings.TrimPrefix(key, "JWT_"))
		} else if strings.HasPrefix(key, "LOG_") {
			section = "log"
			subKey = strings.ToLower(strings.TrimPrefix(key, "LOG_"))
		} else if strings.HasPrefix(key, "SWAGGER_") {
			section = "swagger"
			subKey = strings.ToLower(strings.TrimPrefix(key, "SWAGGER_"))
		} else if strings.HasPrefix(key, "REDIS_") {
			section = "redis"
			subKey = strings.ToLower(strings.TrimPrefix(key, "REDIS_"))
		} else if strings.HasPrefix(key, "SMS_") {
			section = "sms"
			subKey = strings.ToLower(strings.TrimPrefix(key, "SMS_"))
		} else if strings.HasPrefix(key, "WECHAT_") {
			section = "wechat"
			subKey = strings.ToLower(strings.TrimPrefix(key, "WECHAT_"))
		} else if strings.HasPrefix(key, "ALI_PAY_") {
			section = "alipay"
			subKey = strings.ToLower(strings.TrimPrefix(key, "ALI_PAY_"))
		} else if strings.HasPrefix(key, "MEILISEARCH_") {
			section = "meilisearch"
			subKey = strings.ToLower(strings.TrimPrefix(key, "MEILISEARCH_"))
		} else if strings.HasPrefix(key, "BACKEND_") {
			section = "backend"
			subKey = strings.ToLower(strings.TrimPrefix(key, "BACKEND_"))
		} else {
			section = "misc"
			subKey = strings.ToLower(key)
		}

		if section != currentSection {
			currentSection = section
			yamlParts = append(yamlParts, fmt.Sprintf("%s:", section))
		}

		// 将值格式化为YAML格式
		if value == "true" || value == "false" || isNumeric(value) {
			yamlParts = append(yamlParts, fmt.Sprintf("  %s: %s", subKey, value))
		} else {
			yamlParts = append(yamlParts, fmt.Sprintf("  %s: \"%s\"", subKey, escapeYamlString(value)))
		}
	}

	return strings.Join(yamlParts, "\n")
}

// 检查字符串是否是数字
func isNumeric(s string) bool {
	_, err := fmt.Sscanf(s, "%f", new(float64))
	return err == nil
}

// 转义YAML字符串中的特殊字符
func escapeYamlString(s string) string {
	s = strings.ReplaceAll(s, "\\", "\\\\")
	s = strings.ReplaceAll(s, "\"", "\\\"")
	return s
}
