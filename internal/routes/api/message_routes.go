package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterMessageRoutes 注册消息相关路由
func RegisterMessageRoutes(
	router *gin.RouterGroup,
	messageController *api.MessageController,
) {
	// 消息路由组 - 需要认证
	messageGroup := router.Group("/messages")
	messageGroup.Use(middleware.AuthMiddleware())
	{
		// 获取所有消息列表（支持分页）
		messageGroup.GET("", middleware.WithValidation(messageController.GetAllMessages))
		// 获取系统消息列表（支持分页）
		messageGroup.GET("/system", middleware.WithValidation(messageController.GetSystemMessages))
		// 获取账号消息列表（支持分页）
		messageGroup.GET("/account", middleware.WithValidation(messageController.GetAccountMessages))
	}
}
