package models

// Product 产品模型
type Product struct {
	ID           uint   `json:"id" gorm:"primaryKey"`
	Name         string `json:"name" gorm:"size:100;not null;default:'';comment:产品名称"`
	CategoryID   uint   `json:"category_id" gorm:"not null;index;comment:分类ID"`
	ImageURL1    string `json:"image_url1" gorm:"size:255;not null;default:'';comment:图片地址1"`
	ImageURL2    string `json:"image_url2" gorm:"size:255;not null;default:'';comment:图片地址2"`
	Sort         int    `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序字段，数值越小越靠前"`
	CardNoRule   string `json:"card_no_rule" gorm:"size:255;not null;default:'';comment:卡号规则"`
	CardPwdRule  string `json:"card_pwd_rule" gorm:"size:255;not null;default:'';comment:卡密规则"`
	WriteOffDesc string `json:"write_off_desc" gorm:"type:text;comment:核销描述"`
	Demo         string `json:"demo" gorm:"size:255;not null;default:'';comment:演示内容"`
	RuleTip      string `json:"rule_tip" gorm:"size:255;not null;default:'';comment:规则小提示"`
	RuleDesc     string `json:"rule_desc" gorm:"type:text;comment:规则描述"`
	DiscountTip  string `json:"discount_tip" gorm:"size:255;not null;default:'';comment:折扣提示"`

	// 关联分类
	Category Category `json:"category" gorm:"foreignKey:CategoryID;references:ID"`

	Base
}

// TableName 指定表名
func (Product) TableName() string {
	return "product"
}
