#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 命令行参数
MOCK_DB=false
SKIP_DB=false

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    --mock-db)
      MOCK_DB=true
      shift
      ;;
    --skip-db)
      SKIP_DB=true
      shift
      ;;
  esac
done

echo -e "${GREEN}正在启动开发环境...${NC}"

# 检查环境文件是否存在
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}未找到.env文件，正在复制示例文件...${NC}"
    cp .env.example .env
    echo -e "${GREEN}.env文件已创建，请检查配置信息${NC}"
fi

# 如果使用模拟数据库，修改环境变量
if [ "$MOCK_DB" = true ]; then
    echo -e "${YELLOW}使用模拟数据库模式...${NC}"
    # 创建临时.env文件
    sed 's/DB_CONNECTION=mysql/DB_CONNECTION=sqlite/g' .env > .env.tmp
    sed -i '' 's/DB_DATABASE=.*/DB_DATABASE=:memory:/g' .env.tmp
    mv .env.tmp .env
    echo -e "${GREEN}已修改配置为使用SQLite内存数据库${NC}"
fi

# 检查依赖
echo -e "${YELLOW}检查并更新依赖...${NC}"
go mod tidy
if [ $? -ne 0 ]; then
    echo -e "${RED}依赖更新失败${NC}"
    exit 1
fi

# 检查wire是否安装
if ! command -v wire &> /dev/null; then
    echo -e "${YELLOW}未找到wire工具，正在安装...${NC}"
    go install github.com/google/wire/cmd/wire@latest
    if [ $? -ne 0 ]; then
        echo -e "${RED}安装wire失败，请手动安装:${NC}"
        echo "go install github.com/google/wire/cmd/wire@latest"
        exit 1
    fi
    echo -e "${GREEN}wire安装成功${NC}"
fi

# 生成依赖注入代码
echo -e "${YELLOW}正在生成依赖注入代码...${NC}"
cd internal/wire
wire
WIRE_RESULT=$?
cd ../..
if [ $WIRE_RESULT -ne 0 ]; then
    echo -e "${RED}依赖注入代码生成失败${NC}"
    exit 1
fi
echo -e "${GREEN}依赖注入代码生成成功${NC}"

# 检查swag是否安装
if ! command -v swag &> /dev/null; then
    echo -e "${YELLOW}未找到swag工具，正在安装...${NC}"
    go install github.com/swaggo/swag/cmd/swag@latest
    if [ $? -ne 0 ]; then
        echo -e "${RED}安装swag失败，请手动安装:${NC}"
        echo "go install github.com/swaggo/swag/cmd/swag@latest"
    else
        echo -e "${GREEN}swag安装成功${NC}"
    fi
fi

# 检查是否开启Swagger
SWAGGER_ENABLED=$(grep -E "^SWAGGER_ENABLE=(true|false)" .env | cut -d= -f2)
SWAGGER_ENABLED=${SWAGGER_ENABLED:-true} # 默认为true，如果未设置

# 生成Swagger文档
if [ "$SWAGGER_ENABLED" = "true" ]; then
    if command -v swag &> /dev/null; then
        echo -e "${YELLOW}正在生成Swagger API文档...${NC}"
        swag init -g main.go
        if [ $? -ne 0 ]; then
            echo -e "${RED}Swagger文档生成失败${NC}"
        else
            echo -e "${GREEN}Swagger文档生成成功${NC}"
        fi
    else
        echo -e "${YELLOW}跳过Swagger文档生成（未安装swag工具）${NC}"
        echo -e "${YELLOW}您可以安装swag工具：go install github.com/swaggo/swag/cmd/swag@latest${NC}"
    fi
else
    echo -e "${YELLOW}Swagger文档已禁用（SWAGGER_ENABLE=false）${NC}"
fi

# 检查是否安装了air（用于热重载）
if ! command -v air &> /dev/null; then
    echo -e "${YELLOW}未找到air工具，正在安装...${NC}"
    go install github.com/air-verse/air@latest
    if [ $? -ne 0 ]; then
        echo -e "${RED}安装air失败，将使用go run启动${NC}"
        
        # 如果启用了跳过数据库选项
        if [ "$SKIP_DB" = true ]; then
            echo -e "${YELLOW}跳过数据库初始化模式...${NC}"
            echo -e "${YELLOW}正在启动应用（无热重载，跳过数据库）...${NC}"
            # 使用环境变量通知应用跳过数据库初始化
            SKIP_DB_INIT=true go run main.go
        else
            echo -e "${YELLOW}正在启动应用（无热重载）...${NC}"
            go run main.go
        fi
        exit $?
    fi
    echo -e "${GREEN}air安装成功${NC}"
fi

# 使用air启动开发环境（支持热重载）
echo -e "${GREEN}正在使用air启动应用（支持热重载）...${NC}"
if [ "$SWAGGER_ENABLED" = "true" ]; then
    echo -e "${YELLOW}Swagger文档地址: http://localhost:8080/swagger/index.html${NC}"
fi
if [ "$SKIP_DB" = true ]; then
    echo -e "${YELLOW}跳过数据库初始化模式...${NC}"
    SKIP_DB_INIT=true air
else
    air
fi
exit $? 