package services

import (
	"github.com/google/wire"
)

// CodeServiceSet 验证码服务依赖注入集合
var CodeServiceSet = wire.NewSet(
	NewCodeService,
	wire.Bind(new(SMSCodeService), new(CodeService)),
)

// ServiceSet 服务层依赖注入集合
var ServiceSet = wire.NewSet(
	CodeServiceSet,
	NewUploadService,
	NewProductService,
	NewHelpService,
	NewHomeRecommendationService,
	NewMessageService,
	NewAccountService, // 账户服务依赖消息服务
	NewUserService,    // 用户服务依赖消息服务，所以放在最后
	// 未来可以在这里添加其他服务的构造函数
)
