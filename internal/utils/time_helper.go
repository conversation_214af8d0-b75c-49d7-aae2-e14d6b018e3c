package utils

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

// ParseDurationString 解析时长字符串并计算到期时间
// durationStr 格式为 "{年}-{月}-{日}"，例如：
// - "1-0-0" 表示1年
// - "0-1-0" 表示1个月
// - "0-0-7" 表示7天
// - "100-0-0" 表示100年（可用于表示"永久"会员）
func ParseDurationString(startTime time.Time, durationStr string) (time.Time, error) {

	// 解析时长字符串
	parts := strings.Split(durationStr, "-")
	if len(parts) != 3 {
		return time.Time{}, fmt.Errorf("无效的时长格式: %s, 应为 {年}-{月}-{日} 格式", durationStr)
	}

	// 解析年、月、日
	years, err := strconv.Atoi(parts[0])
	if err != nil {
		return time.Time{}, fmt.Errorf("无效的年份值: %s", parts[0])
	}

	months, err := strconv.Atoi(parts[1])
	if err != nil {
		return time.Time{}, fmt.Errorf("无效的月份值: %s", parts[1])
	}

	days, err := strconv.Atoi(parts[2])
	if err != nil {
		return time.Time{}, fmt.Errorf("无效的天数值: %s", parts[2])
	}

	// 计算到期时间
	endTime := startTime.AddDate(years, months, days)
	return endTime, nil
}

// GetDurationDescription 获取时长的可读描述
// 例如：GetDurationDescription("1-0-0") 返回 "1年"
func GetDurationDescription(durationStr string) (string, error) {

	// 解析时长字符串
	parts := strings.Split(durationStr, "-")
	if len(parts) != 3 {
		return "", fmt.Errorf("无效的时长格式: %s, 应为 {年}-{月}-{日} 格式", durationStr)
	}

	// 解析年、月、日
	years, err := strconv.Atoi(parts[0])
	if err != nil {
		return "", fmt.Errorf("无效的年份值: %s", parts[0])
	}

	months, err := strconv.Atoi(parts[1])
	if err != nil {
		return "", fmt.Errorf("无效的月份值: %s", parts[1])
	}

	days, err := strconv.Atoi(parts[2])
	if err != nil {
		return "", fmt.Errorf("无效的天数值: %s", parts[2])
	}

	// 构建描述
	var desc strings.Builder
	if years > 0 {
		desc.WriteString(fmt.Sprintf("%d年", years))
	}
	if months > 0 {
		desc.WriteString(fmt.Sprintf("%d个月", months))
	}
	if days > 0 {
		desc.WriteString(fmt.Sprintf("%d天", days))
	}

	if desc.Len() == 0 {
		return "0天", nil
	}

	return desc.String(), nil
}
