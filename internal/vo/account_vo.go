package vo

import "time"

// AlipayAccountResponse 支付宝账户响应视图对象
type AlipayAccountResponse struct {
	ID            uint      `json:"id" example:"1"`
	UserID        uint      `json:"user_id" example:"1"`
	AlipayAccount string    `json:"alipay_account" example:"***********"`
	CreatedAt     time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt     time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// BankCardResponse 银行卡响应视图对象
type BankCardResponse struct {
	ID          uint      `json:"id" example:"1"`
	UserID      uint      `json:"user_id" example:"1"`
	BankAccount string    `json:"bank_account" example:"6222021234567890123"`
	BankName    string    `json:"bank_name" example:"中国工商银行"`
	CreatedAt   time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt   time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// AlipayAccountListResponse 支付宝账户列表响应视图对象
type AlipayAccountListResponse struct {
	List []AlipayAccountResponse `json:"list"`
}

// BankCardListResponse 银行卡列表响应视图对象
type BankCardListResponse struct {
	List []BankCardResponse `json:"list"`
}
