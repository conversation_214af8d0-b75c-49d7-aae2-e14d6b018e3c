package services

import (
	"recycle-server/internal/enum"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MessageService 消息服务接口
type MessageService interface {
	// GetSystemMessages 获取系统消息列表（支持分页）
	GetSystemMessages(page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error)
	// GetAccountMessages 根据用户ID获取账号消息列表（支持分页）
	GetAccountMessages(userID uint, page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error)
	// CreateAccountMessage 创建账号消息
	CreateAccountMessage(userID uint, title, description string) error
}

// messageService 消息服务实现
type messageService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewMessageService 创建消息服务
func NewMessageService(db *gorm.DB, logger *zap.Logger) MessageService {
	return &messageService{
		db:     db,
		logger: logger,
	}
}

// GetSystemMessages 获取系统消息列表（支持分页）
func (s *messageService) GetSystemMessages(page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error) {
	var messages []models.Message
	var total int64

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// 构建查询条件：只查询系统消息类型
	query := s.db.Model(&models.Message{}).Where("message_type = ?", enum.SystemMessageTypeSystem)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.Error("查询系统消息总数失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 获取分页数据
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&messages).Error; err != nil {
		s.logger.Error("查询系统消息列表失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应格式
	var messageResponses []vo.MessageResponse
	for _, message := range messages {
		messageResponses = append(messageResponses, vo.MessageResponse{
			ID:          message.ID,
			Title:       message.Title,
			Description: message.Description,
			MessageType: message.MessageType,
			UserID:      message.UserID,
			CreatedAt:   message.CreatedAt,
			UpdatedAt:   message.UpdatedAt,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if messageResponses == nil {
		messageResponses = []vo.MessageResponse{}
	}

	// 创建分页响应
	paginatedList := vo.NewPaginatedList(messageResponses, total, page, pageSize)
	return &paginatedList, nil
}

// GetAccountMessages 根据用户ID获取账号消息列表（支持分页）
func (s *messageService) GetAccountMessages(userID uint, page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error) {
	var messages []models.Message
	var total int64

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// 构建查询条件：查询账号消息类型且属于该用户的消息
	query := s.db.Model(&models.Message{}).Where("message_type = ? AND user_id = ?", enum.SystemMessageTypeAccount, userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.Error("查询账号消息总数失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 获取分页数据
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&messages).Error; err != nil {
		s.logger.Error("查询账号消息列表失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应格式
	var messageResponses []vo.MessageResponse
	for _, message := range messages {
		messageResponses = append(messageResponses, vo.MessageResponse{
			ID:          message.ID,
			Title:       message.Title,
			Description: message.Description,
			MessageType: message.MessageType,
			UserID:      message.UserID,
			CreatedAt:   message.CreatedAt,
			UpdatedAt:   message.UpdatedAt,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if messageResponses == nil {
		messageResponses = []vo.MessageResponse{}
	}

	// 创建分页响应
	paginatedList := vo.NewPaginatedList(messageResponses, total, page, pageSize)
	return &paginatedList, nil
}

// CreateAccountMessage 创建账号消息
func (s *messageService) CreateAccountMessage(userID uint, title, description string) error {
	message := &models.Message{
		Title:       title,
		Description: description,
		MessageType: enum.SystemMessageTypeAccount,
		UserID:      userID,
	}

	if err := s.db.Create(message).Error; err != nil {
		s.logger.Error("创建账号消息失败", zap.Uint("userID", userID), zap.String("title", title), zap.Error(err))
		return exception.ErrInternalServer
	}

	s.logger.Info("创建账号消息成功", zap.Uint("userID", userID), zap.String("title", title))
	return nil
}


