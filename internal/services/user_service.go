package services

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"
	"strings"
	"time"

	"recycle-server/config"
	"recycle-server/internal/enum"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/utils"
	"recycle-server/internal/vo"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserService 用户服务接口
type UserService interface {
	GetUserByID(id uint) (*models.User, error)
	GetUserByPhone(phone string) (*models.User, error)

	LoginByCode(ctx context.Context, phone, code string) (*models.User, error)
	LoginByCodeWithToken(ctx context.Context, phone, code string) (*vo.TokenResponse, error)

	UpdateAvatarWithFile(ctx context.Context, userID uint, file *multipart.FileHeader) error
	UpdateUsername(userID uint, username string) error
	UpdateTradePassword(ctx context.Context, userID uint, tradePassword, code string) error
	RealNameAuth(userID uint, realName, idCard string) error

	Logout(ctx context.Context, user *models.User, token string) error
	DeleteUser(ctx context.Context, userID uint) error
}

// userService 用户服务实现
type userService struct {
	userRepo       repository.UserRepository
	uploadService  UploadService
	messageService MessageService
	redisClient    *redis.Client
	jwtService     pkg.JWTService
	codeService    SMSCodeService
	config         *config.Config
	logger         *zap.Logger
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repository.UserRepository,
	uploadService UploadService,
	messageService MessageService,
	redisClient *redis.Client,
	jwtService pkg.JWTService,
	codeService SMSCodeService,
	cfg *config.Config,
	logger *zap.Logger,
) UserService {
	return &userService{
		userRepo:       userRepo,
		uploadService:  uploadService,
		messageService: messageService,
		redisClient:    redisClient,
		jwtService:     jwtService,
		codeService:    codeService,
		config:         cfg,
		logger:         logger,
	}
}

// LoginByCode 通过手机号和验证码登录
// 先判断手机号是否存在，如果存在则直接返回用户
// 如果不存在则创建新用户
func (s *userService) LoginByCode(ctx context.Context, phone, code string) (*models.User, error) {
	// 验证短信验证码
	valid, err := s.codeService.VerifySMSCode(ctx, phone, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	if !valid {
		pkg.Warn("验证码错误或已过期", zap.String("phone", phone))
		return nil, exception.ErrSMSCodeInvalid
	}

	// 查询手机号是否存在
	user, err := s.userRepo.GetByPhone(phone)
	if err == nil {
		// 如果用户存在，检查用户状态
		if user.Status != enum.UserStatusEnabled {
			pkg.Warn("用户状态异常", zap.String("phone", phone), zap.String("status", user.Status.String()))
			return nil, exception.ErrUserStatusDisabled
		}

		pkg.Info("用户验证码登录成功", zap.String("phone", phone))

		// 创建登录成功的账号消息
		go func() {
			err := s.messageService.CreateAccountMessage(
				user.ID,
				"登录成功",
				"您已成功登录账户，欢迎回来！",
			)
			if err != nil {
				pkg.Error("创建登录消息失败", zap.Uint("userID", user.ID), zap.Error(err))
			}
		}()

		return user, nil
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果是其他错误，直接返回
		pkg.Error("查询用户失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserLoginFailed.WithDetail(err.Error())
	}

	// 到这里说明手机号不存在，创建新用户
	return s.createNewUserWithPhone(phone)
}

// createNewUserWithPhone 创建新用户并绑定手机号
func (s *userService) createNewUserWithPhone(phone string) (*models.User, error) {
	pkg.Info("创建新用户并绑定手机号", zap.String("phone", phone))

	// 生成用户名，格式为"用户+手机号后4位"
	username := "用户" + phone[len(phone)-4:]

	// 生成随机头像
	avatar := utils.GenerateRandomAvatar()

	// 创建用户，无需设置密码
	newUser := &models.User{
		Username: username,
		Phone:    phone,
		Avatar:   avatar,
		Status:   enum.UserStatusEnabled,
		UserType: enum.UserTypeRegular, // 设置为普通用户
	}

	if err := s.userRepo.Create(newUser); err != nil {
		pkg.Error("创建用户失败", zap.String("phone", phone), zap.Error(err))
		return nil, exception.ErrUserCreateFailed
	}

	// 创建注册成功的欢迎消息
	go func() {
		err := s.messageService.CreateAccountMessage(
			newUser.ID,
			"欢迎注册",
			"欢迎您注册成为我们的用户！您可以开始使用我们的服务了。如有任何问题，请随时联系客服。",
		)
		if err != nil {
			pkg.Error("创建注册欢迎消息失败", zap.Uint("userID", newUser.ID), zap.Error(err))
		}
	}()

	return newUser, nil
}

// LoginByCodeWithToken 通过手机号和验证码登录并生成token
func (s *userService) LoginByCodeWithToken(ctx context.Context, phone, code string) (*vo.TokenResponse, error) {
	// 先调用LoginByCode方法获取用户
	user, err := s.LoginByCode(ctx, phone, code)
	if err != nil {
		// LoginByCode方法已经记录了错误日志，这里直接返回
		return nil, err
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user.ID)
	if err != nil {
		pkg.Error("生成JWT Token失败", zap.Error(err))
		return nil, exception.ErrTokenGenFailed
	}

	// 返回token响应
	return &vo.TokenResponse{Token: token}, nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id uint) (*models.User, error) {
	user, err := s.userRepo.GetByID(id)
	if err != nil {
		pkg.Error("根据ID获取用户失败", zap.Uint("userId", id), zap.Error(err))
		return nil, err
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		pkg.Warn("用户状态异常", zap.Uint("userId", id), zap.String("status", user.Status.String()))
		return nil, errors.New("用户" + user.Status.String())
	}

	return user, nil
}

// GetUserByPhone 根据手机号获取用户
func (s *userService) GetUserByPhone(phone string) (*models.User, error) {
	return s.userRepo.GetByPhone(phone)
}

// UpdateAvatarWithFile 通过文件上传更新用户头像
func (s *userService) UpdateAvatarWithFile(ctx context.Context, userID uint, file *multipart.FileHeader) error {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 调用上传服务上传头像到OSS
	avatarURL, _, err := s.uploadService.UploadAvatar(ctx, file)
	if err != nil {
		pkg.Error("上传头像失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrAvatarUploadFailed.WithDetail(err.Error())
	}

	// 更新用户头像URL
	if err := s.userRepo.UpdateAvatar(userID, avatarURL); err != nil {
		pkg.Error("更新用户头像失败", zap.Uint("userId", userID), zap.String("avatarURL", avatarURL), zap.Error(err))
		return exception.ErrAvatarUpdateFailed
	}

	pkg.Info("用户头像修改成功", zap.Uint("userId", userID), zap.String("oldAvatar", user.Avatar), zap.String("newAvatar", avatarURL))
	return nil
}

// UpdateUsername 更新用户名
func (s *userService) UpdateUsername(userID uint, username string) error {
	// 验证用户名格式
	if err := s.validateUsername(username); err != nil {
		return err
	}

	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 更新用户名
	if err := s.userRepo.UpdateUsername(userID, username); err != nil {
		pkg.Error("更新用户名失败", zap.Uint("userId", userID), zap.String("username", username), zap.Error(err))
		return exception.ErrUsernameUpdateFailed
	}

	pkg.Info("用户名更新成功", zap.Uint("userId", userID), zap.String("oldUsername", user.Username), zap.String("newUsername", username))

	// 创建用户名修改成功的账号消息
	go func() {
		err := s.messageService.CreateAccountMessage(
			userID,
			"用户名修改成功",
			fmt.Sprintf("您的用户名已成功修改为：%s", username),
		)
		if err != nil {
			pkg.Error("创建用户名修改消息失败", zap.Uint("userID", userID), zap.Error(err))
		}
	}()

	return nil
}

// validateUsername 验证用户名格式
func (s *userService) validateUsername(username string) error {
	// 去除首尾空格
	username = strings.TrimSpace(username)

	// 检查长度
	if len(username) < 2 {
		return exception.ErrUsernameTooShort
	}
	if len(username) > 20 {
		return exception.ErrUsernameTooLong
	}

	// 检查是否为空或只包含空格
	if username == "" {
		return exception.ErrUsernameInvalid
	}

	// 可以在这里添加更多的用户名格式验证规则
	// 例如：不能包含特殊字符、不能是纯数字等

	return nil
}

// Logout 用户退出登录
func (s *userService) Logout(ctx context.Context, user *models.User, token string) error {
	// 解析token以获取过期时间
	claims, err := s.jwtService.ParseToken(token)
	if err != nil {
		pkg.Error("解析token失败", zap.Error(err))
		return exception.ErrTokenInvalid
	}

	// 计算token剩余有效期
	expiration := time.Until(s.jwtService.GetExpirationTime(claims))
	if expiration < 0 {
		expiration = time.Hour // 如果token已过期，设置一个默认的过期时间
	}

	// 将token加入黑名单
	err = s.redisClient.Set(ctx, "black:"+token, user.ID, expiration).Err()
	if err != nil {
		pkg.Error("添加token到黑名单失败", zap.Error(err))
		return exception.ErrInternalServer
	}

	pkg.Info("用户退出登录", zap.Uint("userId", user.ID))
	return nil
}

// DeleteUser 软删除用户账户
func (s *userService) DeleteUser(ctx context.Context, userID uint) error {
	pkg.Info("开始注销用户账户", zap.Uint("userId", userID))

	// 软删除用户
	err := s.userRepo.Delete(userID)
	if err != nil {
		pkg.Error("软删除用户失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	pkg.Info("用户账户注销成功", zap.Uint("userId", userID))
	return nil
}

// UpdateTradePassword 修改交易密码
func (s *userService) UpdateTradePassword(ctx context.Context, userID uint, tradePassword, code string) error {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 验证短信验证码
	valid, err := s.codeService.VerifySMSCode(ctx, user.Phone, code)
	if err != nil {
		pkg.Error("验证码验证失败", zap.String("phone", user.Phone), zap.Error(err))
		return exception.ErrInternalServer
	}

	if !valid {
		pkg.Warn("验证码错误或已过期", zap.String("phone", user.Phone))
		return exception.ErrSMSCodeInvalid
	}

	// 加密交易密码
	hashedPassword, err := utils.HashPassword(tradePassword)
	if err != nil {
		pkg.Error("交易密码加密失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 更新交易密码
	if err := s.userRepo.UpdateTradePassword(userID, hashedPassword); err != nil {
		pkg.Error("更新交易密码失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	pkg.Info("交易密码修改成功", zap.Uint("userId", userID))
	return nil
}

// RealNameAuth 实名认证
func (s *userService) RealNameAuth(userID uint, realName, idCard string) error {
	// 检查用户是否存在
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return exception.ErrUserNotFound
		}
		pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 检查用户状态
	if user.Status != enum.UserStatusEnabled {
		return exception.ErrUserStatusDisabled
	}

	// 验证身份证号码格式
	if !utils.ValidateIDCard(idCard) {
		pkg.Warn("身份证号码格式不正确", zap.Uint("userId", userID), zap.String("idCard", idCard))
		return exception.ErrIDCardInvalid
	}

	// 验证真实姓名格式（简单验证：不能为空，长度2-50字符）
	if len(realName) < 2 || len(realName) > 50 {
		pkg.Warn("真实姓名格式不正确", zap.Uint("userId", userID), zap.String("realName", realName))
		return exception.ErrRealNameInvalid
	}

	// 检查是否已经进行过实名认证
	if user.AuthType != enum.AuthTypeNone {
		pkg.Warn("用户已进行过实名认证", zap.Uint("userId", userID), zap.Int("authType", int(user.AuthType)))
		return exception.ErrAlreadyAuthenticated
	}

	// 更新实名认证信息，设置为普通认证
	if err := s.userRepo.UpdateRealNameAuth(userID, realName, idCard, enum.AuthTypeBasic); err != nil {
		pkg.Error("更新实名认证信息失败", zap.Uint("userId", userID), zap.Error(err))
		return exception.ErrRealNameAuthFailed
	}

	pkg.Info("实名认证成功", zap.Uint("userId", userID), zap.String("realName", realName))
	return nil
}
