package cron

import (
	"context"
	"sync"
	"time"

	"recycle-server/config"
	"recycle-server/internal/repository"

	"github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
)

// CronService 定时任务服务接口
type CronService interface {
	// Start 启动定时任务
	Start(ctx context.Context) error
	// Stop 停止定时任务
	Stop() error
	// IsRunning 检查是否正在运行
	IsRunning() bool
	// AddJob 添加定时任务
	AddJob(spec string, cmd func()) (cron.EntryID, error)
	// RemoveJob 移除定时任务
	RemoveJob(id cron.EntryID)
}

// cronService 定时任务服务实现
type cronService struct {
	config      *config.Config
	logger      *zap.Logger
	cron        *cron.Cron
	userRepo    repository.UserRepository
	redisClient *redis.Client
	running     bool
	mu          sync.RWMutex
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewCronService 创建定时任务服务
func NewCronService(config *config.Config, logger *zap.Logger, userRepo repository.UserRepository, redisClient *redis.Client) CronService {
	// 创建带有秒级精度的 cron 实例
	c := cron.New(cron.WithSeconds())

	return &cronService{
		config:      config,
		logger:      logger,
		cron:        c,
		userRepo:    userRepo,
		redisClient: redisClient,
	}
}

// Start 启动定时任务
func (c *cronService) Start(ctx context.Context) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.running {
		c.logger.Warn("定时任务已经在运行中")
		return nil
	}

	c.logger.Info("启动定时任务服务")

	// 创建可取消的上下文
	c.ctx, c.cancel = context.WithCancel(ctx)

	// TODO: 在这里添加需要的定时任务

	// 启动 cron 调度器
	c.cron.Start()
	c.running = true

	// 启动监听上下文取消的 goroutine
	go func() {
		<-c.ctx.Done()
		c.logger.Info("收到上下文取消信号，停止定时任务")
		c.Stop()
	}()

	c.logger.Info("定时任务服务启动成功")
	return nil
}

// Stop 停止定时任务
func (c *cronService) Stop() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		c.logger.Warn("定时任务未在运行")
		return nil
	}

	c.logger.Info("正在停止定时任务服务")

	// 停止 cron 调度器
	cronCtx := c.cron.Stop()

	// 等待所有任务完成，最多等待5秒
	select {
	case <-cronCtx.Done():
		c.logger.Info("所有定时任务已完成")
	case <-time.After(5 * time.Second):
		c.logger.Warn("等待定时任务完成超时")
	}

	// 取消上下文
	if c.cancel != nil {
		c.cancel()
	}

	c.running = false
	c.logger.Info("定时任务服务已停止")
	return nil
}

// IsRunning 检查是否正在运行
func (c *cronService) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.running
}

// AddJob 添加定时任务
func (c *cronService) AddJob(spec string, cmd func()) (cron.EntryID, error) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return 0, nil
	}

	return c.cron.AddFunc(spec, cmd)
}

// RemoveJob 移除定时任务
func (c *cronService) RemoveJob(id cron.EntryID) {
	c.mu.Lock()
	defer c.mu.Unlock()

	if !c.running {
		return
	}

	c.cron.Remove(id)
}
