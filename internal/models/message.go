package models

import (
	"recycle-server/internal/enum"
)

// Message 消息模型
type Message struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Title       string `json:"title" gorm:"size:255;not null;comment:消息标题"`
	Description string `json:"description" gorm:"type:text;comment:消息描述"`
	MessageType enum.SystemMessageType `json:"message_type" gorm:"type:tinyint(1);default:1;index;comment:消息类型 1:系统消息 2:账号消息"`

	Base
}

// TableName 指定表名
func (Message) TableName() string {
	return "message"
}
