package exception

import (
	"fmt"
	"strings"
)

// ApiError 实现error接口的API错误结构体
type ApiError struct {
	HttpCode int    `json:"-"`       // HTTP状态码，不输出到JSON
	Code     int    `json:"code"`    // 业务错误码
	Message  string `json:"message"` // 错误消息
}

// Error 实现error接口
func (e *ApiError) Error() string {
	return e.Message
}

// New 创建一个API错误
func New(httpCode, code int, message string) *ApiError {
	return &ApiError{
		HttpCode: httpCode,
		Code:     code,
		Message:  message,
	}
}

// WithMessage 设置错误消息并返回新的错误
func (e *ApiError) WithMessage(message string) *ApiError {
	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  message,
	}
}

// WithDetail 在错误消息中附加详细文本信息
func (e *ApiError) WithDetail(detail string) *ApiError {
	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  fmt.Sprintf("%s: %s", e.Message, detail),
	}
}

// WithDetailsMap 使用字符串映射添加详细信息
func (e *ApiError) WithDetailsMap(details map[string]string) *ApiError {
	message := e.Message

	if len(details) > 0 {
		errorDetails := ""
		for field, errMsg := range details {
			if errorDetails != "" {
				errorDetails += "; "
			}
			errorDetails += field + ": " + errMsg
		}
		message = message + " (" + errorDetails + ")"
	}

	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  message,
	}
}

// WithDetailsAnyMap 使用任意值映射添加详细信息
func (e *ApiError) WithDetailsAnyMap(details map[string]any) *ApiError {
	message := e.Message

	if len(details) > 0 {
		errorDetails := ""
		for field, value := range details {
			if errorDetails != "" {
				errorDetails += "; "
			}
			errorDetails += field + ": " + fmt.Sprintf("%v", value)
		}
		message = message + " (" + errorDetails + ")"
	}

	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  message,
	}
}

// WithDetailsSlice 使用字符串切片添加详细信息
func (e *ApiError) WithDetailsSlice(details []string) *ApiError {
	message := e.Message

	if len(details) > 0 {
		message = message + " (" + strings.Join(details, "; ") + ")"
	}

	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  message,
	}
}

// 泛型函数定义 - 用于不同类型的错误详情处理
// -------------------------------------------

// WithStringDetails 添加字符串类型的详细信息
func WithStringDetails[T ~string](e *ApiError, detail T) *ApiError {
	return e.WithDetail(string(detail))
}

// WithMapDetails 添加映射类型的详细信息
func WithMapDetails[K ~string, V any](e *ApiError, details map[K]V) *ApiError {
	// 将映射转换为字符串表示
	message := e.Message

	if len(details) > 0 {
		errorDetails := ""
		for field, value := range details {
			if errorDetails != "" {
				errorDetails += "; "
			}
			errorDetails += string(field) + ": " + fmt.Sprintf("%v", value)
		}
		message = message + " (" + errorDetails + ")"
	}

	return &ApiError{
		HttpCode: e.HttpCode,
		Code:     e.Code,
		Message:  message,
	}
}

// WithSliceDetails 添加切片类型的详细信息
func WithSliceDetails[T fmt.Stringer](e *ApiError, details []T) *ApiError {
	if len(details) == 0 {
		return e
	}

	strDetails := make([]string, len(details))
	for i, detail := range details {
		strDetails[i] = detail.String()
	}

	return e.WithDetailsSlice(strDetails)
}
