definitions:
  dto.AddAlipayAccountRequest:
    properties:
      alipay_account:
        example: "***********"
        maxLength: 100
        minLength: 1
        type: string
    required:
    - alipay_account
    type: object
  dto.AddBankCardRequest:
    properties:
      bank_account:
        example: "6222021234567890123"
        maxLength: 30
        minLength: 1
        type: string
      bank_name:
        example: 中国工商银行
        maxLength: 100
        minLength: 1
        type: string
    required:
    - bank_account
    - bank_name
    type: object
  dto.DeleteAlipayAccountRequest:
    properties:
      id:
        example: 1
        minimum: 1
        type: integer
      trade_password:
        example: "123456"
        maxLength: 20
        minLength: 6
        type: string
    required:
    - id
    - trade_password
    type: object
  dto.DeleteBankCardRequest:
    properties:
      id:
        example: 1
        minimum: 1
        type: integer
      trade_password:
        example: "123456"
        maxLength: 20
        minLength: 6
        type: string
    required:
    - id
    - trade_password
    type: object
  dto.LoginCodeRequest:
    properties:
      code:
        example: "123456"
        type: string
      phone:
        example: "***********"
        type: string
    required:
    - code
    - phone
    type: object
  dto.RealNameAuthRequest:
    properties:
      id_card:
        example: "110101199001011234"
        type: string
      real_name:
        example: 张三
        maxLength: 50
        minLength: 2
        type: string
    required:
    - id_card
    - real_name
    type: object
  dto.SendSMSCodeRequest:
    properties:
      phone:
        example: "***********"
        type: string
    required:
    - phone
    type: object
  dto.UpdateTradePasswordRequest:
    properties:
      code:
        example: "123456"
        type: string
      trade_password:
        example: "123456"
        maxLength: 20
        minLength: 6
        type: string
    required:
    - code
    - trade_password
    type: object
  dto.UpdateUsernameRequest:
    properties:
      username:
        example: 新用户名
        maxLength: 20
        minLength: 2
        type: string
    required:
    - username
    type: object
  enum.AuthType:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      AuthTypeAdvanced: 高级认证
      AuthTypeBasic: 普通认证
      AuthTypeNone: 未认证
    x-enum-varnames:
    - AuthTypeNone
    - AuthTypeBasic
    - AuthTypeAdvanced
  enum.UserType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-comments:
      UserTypeRegular: 普通用户
      UserTypeVIP: 会员
    x-enum-varnames:
    - UserTypeRegular
    - UserTypeVIP
  vo.AlipayAccountListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo.AlipayAccountResponse'
        type: array
    type: object
  vo.AlipayAccountResponse:
    properties:
      alipay_account:
        example: "***********"
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      user_id:
        example: 1
        type: integer
    type: object
  vo.BankCardListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo.BankCardResponse'
        type: array
    type: object
  vo.BankCardResponse:
    properties:
      bank_account:
        example: "6222021234567890123"
        type: string
      bank_name:
        example: 中国工商银行
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      user_id:
        example: 1
        type: integer
    type: object
  vo.BankListResponse:
    properties:
      banks:
        example:
        - 中国银行
        - 工商银行
        - 农业银行
        - 建设银行
        items:
          type: string
        type: array
    type: object
  vo.CategoryResponse:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 电商卡
        type: string
    type: object
  vo.ErrorAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 400
        type: integer
      data:
        description: 响应数据，通常为null
      message:
        description: 错误消息
        example: 请求参数错误
        type: string
    type: object
  vo.FaceValueResponse:
    properties:
      id:
        example: 1
        type: integer
      my_max_rate:
        example: "95.5"
        type: string
      my_min_rate:
        example: "94.0"
        type: string
      my_rate:
        example: "94.8"
        type: string
      name:
        example: 100元
        type: string
      status:
        example: true
        type: boolean
      value:
        example: 100
        type: number
    type: object
  vo.HelpListResponse:
    properties:
      list:
        description: 帮助信息列表
        items:
          $ref: '#/definitions/vo.HelpResponse'
        type: array
    type: object
  vo.HelpResponse:
    properties:
      description:
        description: 描述
        example: 平台现在支持微信、支付宝、网银提现...
        type: string
      id:
        description: 帮助ID
        example: 1
        type: integer
      image_url:
        description: 图片链接
        example: https://example.com/help.jpg
        type: string
      sort:
        description: 排序
        example: 1
        type: integer
      title:
        description: 标题
        example: 平台是如何结算,结算时间是多久?
        type: string
    type: object
  vo.HomeRecommendationResponse:
    properties:
      description:
        description: 描述
        example: 推荐描述信息
        type: string
      id:
        description: 推荐ID
        example: 1
        type: integer
      image_url:
        description: 图片链接
        example: https://example.com/image.jpg
        type: string
      jump_url:
        description: 跳转链接
        example: https://example.com/jump
        type: string
      sort:
        description: 排序
        example: 1
        type: integer
      title:
        description: 文案标题
        example: 热门推荐标题
        type: string
    type: object
  vo.HotProductListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo.HotProductResponse'
        type: array
    type: object
  vo.HotProductResponse:
    properties:
      discount_tip:
        example: 9.5折起
        type: string
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image.jpg
        type: string
      name:
        example: 京东E卡
        type: string
    type: object
  vo.ProductCategoryResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/vo.ProductSummaryResponse'
        type: array
      id:
        example: 1
        type: integer
      name:
        example: 电商卡
        type: string
    type: object
  vo.ProductDetailResponse:
    properties:
      card_no_rule:
        example: 16位数字
        type: string
      card_pwd_rule:
        example: 8位数字
        type: string
      category:
        $ref: '#/definitions/vo.CategoryResponse'
      category_id:
        example: 1
        type: integer
      demo:
        example: 演示内容
        type: string
      discount_tip:
        example: 9.5折起
        type: string
      face_values:
        items:
          $ref: '#/definitions/vo.FaceValueResponse'
        type: array
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image1.jpg
        type: string
      image_url2:
        example: https://example.com/image2.jpg
        type: string
      name:
        example: 京东E卡
        type: string
      rule_desc:
        example: 详细规则描述
        type: string
      rule_tip:
        example: 规则提示
        type: string
      write_off_desc:
        example: 核销说明
        type: string
    type: object
  vo.ProductSearchListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo.ProductSearchResponse'
        type: array
    type: object
  vo.ProductSearchResponse:
    properties:
      discount_tip:
        example: 9.5折起
        type: string
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image.jpg
        type: string
      name:
        example: 京东E卡
        type: string
    type: object
  vo.ProductSummaryResponse:
    properties:
      id:
        example: 1
        type: integer
      image_url1:
        example: https://example.com/image.jpg
        type: string
      name:
        example: 京东E卡
        type: string
    type: object
  vo.RecentOrderListResponse:
    properties:
      list:
        description: 最近订单列表
        items:
          $ref: '#/definitions/vo.RecentOrderResponse'
        type: array
    type: object
  vo.RecentOrderResponse:
    properties:
      create_time:
        description: 创建时间
        example: "2025-07-30 21:34:54"
        type: string
      face_value:
        description: 面值
        example: 1000
        type: number
      msg:
        description: 消息
        example: 陈**提交了1000京东e卡-快销
        type: string
      pname:
        description: 产品名称
        example: 京东e卡-快销
        type: string
    type: object
  vo.SuccessAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 200
        type: integer
      data:
        description: 响应数据
      message:
        description: 响应消息
        example: 操作成功
        type: string
    type: object
  vo.SystemConfigResponse:
    properties:
      hero_image_url:
        example: https://example.com/public/system/666.jpg
        type: string
      home_recommendations:
        items:
          $ref: '#/definitions/vo.HomeRecommendationResponse'
        type: array
      hot_search:
        example:
        - 京东e卡
        - 携程
        - 微信立减金
        - 支付宝消费券
        items:
          type: string
        type: array
      icp_number:
        example: 湘ICP备2023018441号-2X
        type: string
      notice:
        example: 【京顺回收】160余种卡券回收，如需了解更多问题，请点击咨询人工客服：09:00-24:00                                        24小时在线回收卡券，卡券提交后所有交易均可自动处理、24小时均可提现，1-3分钟内立即到账！
        type: string
    type: object
  vo.TokenResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  vo.UploadResponse:
    properties:
      filename:
        description: 文件名
        example: 123456789.jpg
        type: string
      url:
        description: 文件URL
        example: https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg
        type: string
    type: object
  vo.UserResponse:
    properties:
      auth_type:
        allOf:
        - $ref: '#/definitions/enum.AuthType'
        description: 认证类型 1:未认证 2:普通认证 3:高级认证
        example: 1
      avatar:
        example: http://example.com/avatar.jpg
        type: string
      balance:
        description: 余额
        example: 100.5
        type: number
      created_at:
        description: 创建时间
        example: "2023-01-01T12:00:00Z"
        type: string
      friend_count:
        description: 好友数
        example: 10
        type: integer
      id:
        example: 1
        type: integer
      id_card:
        description: 身份证号码
        example: "110101199001011234"
        type: string
      open_id:
        example: oNHwxjgrzgL9H_A2pGLSMuME-X-Q
        type: string
      phone:
        example: "***********"
        type: string
      promotion_earning:
        description: 推广收益
        example: 50
        type: number
      real_name:
        description: 真实姓名
        example: 张三
        type: string
      user_type:
        allOf:
        - $ref: '#/definitions/enum.UserType'
        description: 用户类型 1:普通用户 2:中级会员 3:高级会员
        example: 1
      username:
        example: johndoe
        type: string
    type: object
host: localhost:8084
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: https://github.com/avrilko-go/recycle-server
  description: 京海回收服务器 API 文档
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
  title: 京海回收服务器 API
  version: "1.0"
paths:
  /api/accounts/alipay:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的所有支付宝账户
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.AlipayAccountListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取所有支付宝账户
      tags:
      - API/账户管理
    post:
      consumes:
      - application/json
      description: 为当前登录用户添加支付宝账户
      parameters:
      - description: 添加支付宝账户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.AddAlipayAccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 添加支付宝账户
      tags:
      - API/账户管理
  /api/accounts/alipay/{id}:
    delete:
      consumes:
      - application/json
      description: 删除当前登录用户的指定支付宝账户，需要验证交易密码
      parameters:
      - description: 支付宝账户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 删除支付宝账户请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.DeleteAlipayAccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误或交易密码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 账户不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除支付宝账户
      tags:
      - API/账户管理
  /api/accounts/bank-card:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的所有银行卡
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.BankCardListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取所有银行卡
      tags:
      - API/账户管理
    post:
      consumes:
      - application/json
      description: 为当前登录用户添加银行卡
      parameters:
      - description: 添加银行卡请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.AddBankCardRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 添加银行卡
      tags:
      - API/账户管理
  /api/accounts/bank-card/{id}:
    delete:
      consumes:
      - application/json
      description: 删除当前登录用户的指定银行卡，需要验证交易密码
      parameters:
      - description: 银行卡ID
        in: path
        name: id
        required: true
        type: integer
      - description: 删除银行卡请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.DeleteBankCardRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误或交易密码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 银行卡不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除银行卡
      tags:
      - API/账户管理
  /api/auth/login-code:
    post:
      consumes:
      - application/json
      description: 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
      parameters:
      - description: 登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.LoginCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回token
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.TokenResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 验证码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 验证码登录
      tags:
      - API/认证管理
  /api/auth/sms/code:
    post:
      consumes:
      - application/json
      description: 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.SendSMSCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 发送短信验证码
      tags:
      - API/认证管理
  /api/common/banks:
    get:
      consumes:
      - application/json
      description: 从系统配置文件中读取所有银行列表信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.BankListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有银行列表
      tags:
      - API/通用接口
  /api/common/helps:
    get:
      consumes:
      - application/json
      description: 获取所有帮助信息，按排序字段升序排列
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.HelpListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有帮助信息
      tags:
      - API/通用接口
  /api/common/recent-orders:
    get:
      consumes:
      - application/json
      description: 获取最近订单信息，包含面值、时间、消息、产品名称等
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.RecentOrderListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取最近订单
      tags:
      - API/通用接口
  /api/common/system-config:
    get:
      consumes:
      - application/json
      description: 获取系统配置信息，包括热门搜索、公告、首页推荐等
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.SystemConfigResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取系统配置
      tags:
      - API/通用接口
  /api/products/{id}:
    get:
      consumes:
      - application/json
      description: 根据产品ID获取产品的详细信息，包含面值信息
      parameters:
      - description: 产品ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.ProductDetailResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 产品不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据ID获取产品详情
      tags:
      - API/产品管理
  /api/products/categories:
    get:
      consumes:
      - application/json
      description: 获取所有分类及其下的产品信息，按分类分组返回
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo.ProductCategoryResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取带分类分组的产品信息
      tags:
      - API/产品管理
  /api/products/hot:
    get:
      consumes:
      - application/json
      description: 获取所有热门产品列表，按排序字段排序返回
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.HotProductListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有热门产品
      tags:
      - API/产品管理
  /api/products/search:
    get:
      consumes:
      - application/json
      description: 根据关键词模糊搜索产品名称，返回匹配的产品列表
      parameters:
      - description: 搜索关键词
        in: query
        name: keyword
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 搜索成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.ProductSearchListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 搜索产品
      tags:
      - API/产品管理
  /api/upload/avatar:
    post:
      consumes:
      - multipart/form-data
      description: 上传用户头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.UploadResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 上传头像
      tags:
      - API/文件上传
  /api/users/avatar:
    put:
      consumes:
      - multipart/form-data
      description: 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户头像
      tags:
      - API/用户管理
  /api/users/delete:
    delete:
      consumes:
      - application/json
      description: 软删除当前登录用户的账户，注销后无法恢复
      produces:
      - application/json
      responses:
        "200":
          description: 注销成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 注销账户
      tags:
      - API/用户管理
  /api/users/info:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo.UserResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取当前登录用户信息
      tags:
      - API/用户管理
  /api/users/logout:
    post:
      consumes:
      - application/json
      description: 用户退出登录，清除登录状态
      produces:
      - application/json
      responses:
        "200":
          description: 退出成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 退出登录
      tags:
      - API/用户管理
  /api/users/real-name-auth:
    post:
      consumes:
      - application/json
      description: 提交真实姓名和身份证号进行实名认证，认证成功后用户认证类型变更为普通认证
      parameters:
      - description: 实名认证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.RealNameAuthRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 认证成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误或已认证
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 实名认证
      tags:
      - API/用户管理
  /api/users/trade-password:
    put:
      consumes:
      - application/json
      description: 修改当前登录用户的交易密码，需要手机验证码验证
      parameters:
      - description: 修改交易密码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateTradePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权或验证码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改交易密码
      tags:
      - API/用户管理
  /api/users/username:
    put:
      consumes:
      - application/json
      description: 修改当前登录用户的用户名
      parameters:
      - description: 修改用户名请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.UpdateUsernameRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户名
      tags:
      - API/用户管理
schemes:
- http
- https
swagger: "2.0"
