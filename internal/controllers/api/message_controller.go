package api

import (
	"github.com/gin-gonic/gin"
	"recycle-server/internal/dto"
	"recycle-server/internal/enum"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/pkg"
	"recycle-server/internal/response"
	"recycle-server/internal/services"
)

// MessageController 消息控制器
type MessageController struct {
	messageService services.MessageService
}

// NewMessageController 创建消息控制器
func NewMessageController(messageService services.MessageService) *MessageController {
	return &MessageController{
		messageService: messageService,
	}
}

// GetSystemMessages 获取系统消息列表
// @Summary 获取系统消息列表
// @Description 获取系统消息列表，支持分页
// @Tags API/消息管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页条数" default(10) minimum(1) maximum(100)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.PaginatedList[vo.MessageResponse]} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/messages/system [get]
func (c *MessageController) GetSystemMessages(ctx *gin.Context, req dto.MessageListRequest) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 调用服务层获取系统消息
	result, err := c.messageService.GetMessagesByType(user.ID, enum.SystemMessageTypeSystem, page, pageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}

// GetAccountMessages 获取账号消息列表
// @Summary 获取账号消息列表
// @Description 获取账号消息列表，支持分页
// @Tags API/消息管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页条数" default(10) minimum(1) maximum(100)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.PaginatedList[vo.MessageResponse]} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/messages/account [get]
func (c *MessageController) GetAccountMessages(ctx *gin.Context, req dto.MessageListRequest) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 调用服务层获取账号消息
	result, err := c.messageService.GetMessagesByType(user.ID, enum.SystemMessageTypeAccount, page, pageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}

// GetAllMessages 获取所有消息列表
// @Summary 获取所有消息列表
// @Description 获取所有消息列表（包括系统消息和账号消息），支持分页
// @Tags API/消息管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1) minimum(1)
// @Param page_size query int false "每页条数" default(10) minimum(1) maximum(100)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.PaginatedList[vo.MessageResponse]} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/messages [get]
func (c *MessageController) GetAllMessages(ctx *gin.Context, req dto.MessageListRequest) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 调用服务层获取所有消息
	result, err := c.messageService.GetAllMessages(user.ID, page, pageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}
