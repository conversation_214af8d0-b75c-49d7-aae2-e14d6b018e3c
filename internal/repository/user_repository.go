package repository

import (
	"recycle-server/config"
	"recycle-server/internal/enum"
	"recycle-server/internal/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	Create(user *models.User) error
	GetByID(id uint) (*models.User, error)
	GetByPhone(phone string) (*models.User, error)
	GetByOpenID(openID string) (*models.User, error)
	Update(user *models.User) error
	Delete(id uint) error
	List(page, pageSize int) ([]*models.User, int64, error)
	UpdateAvatar(userID uint, avatarURL string) error
	ExistsByUsername(username string) (bool, error)
	UpdateUsername(userID uint, username string) error
	UpdateTradePassword(userID uint, hashedPassword string) error
	UpdateRealNameAuth(userID uint, realName, idCard string, authType enum.AuthType) error
	// BatchUpdateUserType 批量更新用户类型
	BatchUpdateUserType(userIDs []uint, userType enum.UserType) error
}

// userRepository 用户仓储实现
type userRepository struct {
	db     *gorm.DB
	config *config.Config
	logger *zap.Logger
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *gorm.DB, cfg *config.Config, logger *zap.Logger) UserRepository {
	return &userRepository{
		db:     db,
		config: cfg,
		logger: logger,
	}
}

// Create 创建用户
func (r *userRepository) Create(user *models.User) error {
	return r.db.Create(user).Error
}

// GetByID 根据ID获取用户
func (r *userRepository) GetByID(id uint) (*models.User, error) {
	var user models.User
	if err := r.db.First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByUsername 根据用户名获取用户
func (r *userRepository) GetByUsername(username string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByPhone 根据手机号获取用户
func (r *userRepository) GetByPhone(phone string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("phone = ?", phone).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// GetByOpenID 根据微信OpenID获取用户
func (r *userRepository) GetByOpenID(openID string) (*models.User, error) {
	var user models.User
	if err := r.db.Where("open_id = ?", openID).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *models.User) error {
	return r.db.Save(user).Error
}

// Delete 删除用户
func (r *userRepository) Delete(id uint) error {
	return r.db.Delete(&models.User{}, id).Error
}

// List 获取用户列表
func (r *userRepository) List(page, pageSize int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	offset := (page - 1) * pageSize

	// 获取总数
	if err := r.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := r.db.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// ExistsByUsername 检查用户名是否已存在
func (r *userRepository) ExistsByUsername(username string) (bool, error) {
	var count int64
	err := r.db.Model(&models.User{}).Where("username = ?", username).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// UpdateUsername 更新用户名
func (r *userRepository) UpdateUsername(userID uint, username string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("username", username).Error
}

// UpdateAvatar 更新用户头像
func (r *userRepository) UpdateAvatar(userID uint, avatarURL string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("avatar", avatarURL).Error
}

// UpdateTradePassword 更新交易密码
func (r *userRepository) UpdateTradePassword(userID uint, hashedPassword string) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Update("trade_password", hashedPassword).Error
}

// UpdateRealNameAuth 更新实名认证信息
func (r *userRepository) UpdateRealNameAuth(userID uint, realName, idCard string, authType enum.AuthType) error {
	return r.db.Model(&models.User{}).Where("id = ?", userID).Updates(map[string]interface{}{
		"real_name": realName,
		"id_card":   idCard,
		"auth_type": authType,
	}).Error
}

// BatchUpdateUserType 批量更新用户类型
func (r *userRepository) BatchUpdateUserType(userIDs []uint, userType enum.UserType) error {
	if len(userIDs) == 0 {
		return nil
	}

	return r.db.Model(&models.User{}).
		Where("id IN ?", userIDs).
		Update("user_type", userType).Error
}
