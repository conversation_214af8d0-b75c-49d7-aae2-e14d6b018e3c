package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterUserRoutes 注册用户相关路由
func RegisterUserRoutes(
	router *gin.RouterGroup,
	userController *api.UserController,
	authController *api.AuthController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 用户路由组
	userGroup := router.Group("/users")

	// 登录用户路由组，使用JWT用户认证中间件
	userGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 获取当前登录用户信息
		userGroup.GET("/info", middleware.WithoutValidation(userController.GetUserInfo))
		// 修改用户名
		userGroup.PUT("/username", middleware.WithValidation(userController.UpdateUsername))
		// 修改用户头像
		userGroup.PUT("/avatar", middleware.WithValidation(userController.UpdateAvatar))
		// 修改交易密码
		userGroup.PUT("/trade-password", middleware.WithValidation(userController.UpdateTradePassword))
		// 实名认证
		userGroup.POST("/real-name-auth", middleware.WithValidation(userController.RealNameAuth))

		// 退出登录
		userGroup.POST("/logout", middleware.WithoutValidation(userController.Logout))

		// 注销账户
		userGroup.DELETE("/delete", middleware.WithoutValidation(userController.DeleteAccount))
	}
}
