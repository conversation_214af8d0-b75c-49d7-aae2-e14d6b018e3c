package pkg

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhTranslations "github.com/go-playground/validator/v10/translations/zh"
	"go.uber.org/zap"
)

var (
	trans ut.Translator
)

// InitValidator 初始化验证器
func InitValidator() error {
	// 获取gin的验证器
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		// 注册一个获取json tag的自定义方法
		v.RegisterTagNameFunc(func(fld reflect.StructField) string {
			// 优先使用label标签作为字段名
			label := fld.Tag.Get("label")
			if label != "" {
				return label
			}
			// 其次使用json标签
			name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
			if name == "-" {
				return ""
			}
			return name
		})

		zhT := zh.New() // 中文翻译器
		enT := en.New() // 英文翻译器

		// 第一个参数是备用的语言环境，后面的参数是应该支持的语言环境
		uni := ut.New(enT, zhT, enT)

		// 获取中文翻译器
		trans, _ = uni.GetTranslator("zh")

		// 注册翻译器
		err := zhTranslations.RegisterDefaultTranslations(v, trans)
		if err != nil {
			return err
		}

		// 注册自定义翻译
		registerCustomTranslations(v)

		return nil
	}
	return fmt.Errorf("验证器初始化失败")
}

// registerCustomTranslations 注册自定义翻译
func registerCustomTranslations(v *validator.Validate) {
	// 自定义验证器翻译，可以自定义各种错误消息
	translations := []struct {
		tag         string
		translation string
	}{
		{
			tag:         "required",
			translation: "{0}不能为空",
		},
		{
			tag:         "email",
			translation: "{0}格式错误",
		},
		{
			tag:         "min",
			translation: "{0}长度不能小于{1}",
		},
		{
			tag:         "max",
			translation: "{0}长度不能大于{1}",
		},
		{
			tag:         "alphanum",
			translation: "{0}只能是字母和数字",
		},
		{
			tag:         "numeric",
			translation: "{0}必须是数字",
		},
		{
			tag:         "gt",
			translation: "{0}必须大于{1}",
		},
		{
			tag:         "lt",
			translation: "{0}必须小于{1}",
		},
		{
			tag:         "gte",
			translation: "{0}必须大于或等于{1}",
		},
		{
			tag:         "lte",
			translation: "{0}必须小于或等于{1}",
		},
		{
			tag:         "oneof",
			translation: "{0}必须是[{1}]中的一个",
		},
	}

	for _, t := range translations {
		registerTranslation(v, t.tag, t.translation)
	}
}

// registerTranslation 注册翻译器
func registerTranslation(v *validator.Validate, tag string, message string) {
	_ = v.RegisterTranslation(tag, trans, func(ut ut.Translator) error {
		return ut.Add(tag, message, true)
	}, func(ut ut.Translator, fe validator.FieldError) string {
		t, _ := ut.T(fe.Tag(), fe.Field(), fe.Param())
		return t
	})
}

// Translate 翻译错误信息
func Translate(err error) map[string]string {
	var result = make(map[string]string)

	// 处理EOF错误，这通常是因为请求体为空
	if err.Error() == "EOF" {
		Warn("请求体为空，但请求需要请求体")
		result["error"] = "请求体不能为空"
		return result
	}

	errors, ok := err.(validator.ValidationErrors)
	if !ok {
		// 如果它不是验证器的错误类型，记录并返回通用错误
		Error("翻译验证错误失败，错误类型未知", zap.Error(err))
		result["error"] = "请求参数错误: " + err.Error()
		return result
	}

	// 翻译每个错误字段
	for _, e := range errors {
		// 使用原始字段名作为键（如 Username，而不是"用户名"）
		field := e.StructField()
		// 但使用翻译后的值（如"用户名不能为空"而不是"Username不能为空"）
		result[field] = e.Translate(trans)
	}
	return result
}

// TranslateURIError 翻译URI参数错误信息
func TranslateURIError(err error) map[string]string {
	var result = make(map[string]string)

	// 尝试将错误转换为验证器错误
	errors, ok := err.(validator.ValidationErrors)
	if ok {
		// 如果是验证器错误，使用标准翻译
		for _, e := range errors {
			field := e.StructField()
			result[field] = e.Translate(trans)
		}
		return result
	}

	// 如果不是验证器错误，尝试解析错误消息
	errMsg := err.Error()

	// 处理EOF错误，这通常是因为请求体为空
	if errMsg == "EOF" {
		Warn("请求体为空，但请求需要请求体")
		result["error"] = "请求体不能为空"
		return result
	}

	// 尝试解析常见的URI绑定错误
	if strings.Contains(errMsg, "uri:") {
		// 例如："Key: 'DeleteAccountRequest.ID' Error:Field validation for 'ID' failed on the 'required' tag"
		parts := strings.Split(errMsg, "'")
		if len(parts) >= 4 {
			// 提取字段名，如 "DeleteAccountRequest.ID"
			fullField := parts[1]
			fieldParts := strings.Split(fullField, ".")
			if len(fieldParts) >= 2 {
				field := fieldParts[1] // 提取字段名，如 "ID"
				result[field] = field + "不能为空"
				return result
			}
		}
	}

	// 如果是其他类型的错误，尝试提取更多信息
	Warn("无法解析的URI错误", zap.String("error", errMsg))

	// 如果无法解析，返回通用错误
	Error("翻译URI验证错误失败", zap.Error(err))
	result["error"] = "路径参数错误: " + errMsg
	return result
}
