package routes

import (
	"recycle-server/config"
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	apiRoutes "recycle-server/internal/routes/api"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// InitRoutes 初始化所有路由
func InitRoutes(
	router *gin.Engine,
	cfg *config.Config,
	userController *api.UserController,
	authController *api.AuthController,
	uploadController *api.UploadController,
	productController *api.ProductController,
	commonController *api.CommonController,
	accountController *api.AccountController,
	messageController *api.MessageController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 注册全局中间件
	router.Use(middleware.ErrorHandler()) // 错误处理中间件必须放在最前面
	router.Use(gin.Recovery())
	router.Use(gin.Logger())

	// 静态文件服务 - 提供 public 目录下的文件访问
	router.Static("/public", "./public")

	// API路由组
	apiGroup := router.Group("/api")

	// 注册认证路由
	apiRoutes.RegisterAuthRoutes(apiGroup, authController, jwtService, userService, userRepo, redisClient)

	// 注册用户路由
	apiRoutes.RegisterUserRoutes(apiGroup, userController, authController, jwtService, userService, userRepo, redisClient)

	// 注册上传路由
	apiRoutes.RegisterUploadRoutes(apiGroup, uploadController, jwtService, userService, userRepo, redisClient)

	// 注册产品路由
	apiRoutes.RegisterProductRoutes(apiGroup, productController)

	// 注册通用路由
	apiRoutes.RegisterCommonRoutes(apiGroup, commonController)

	// 注册账户路由
	apiRoutes.RegisterAccountRoutes(apiGroup, accountController, jwtService, userService, userRepo, redisClient)

	// 注册消息路由
	apiRoutes.RegisterMessageRoutes(apiGroup, messageController)
}
