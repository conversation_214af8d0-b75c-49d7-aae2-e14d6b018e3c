package response

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Response 通用响应结构
type Response[T any] struct {
	Code    int    `json:"code" example:"0"`
	Message string `json:"message" example:"操作成功"`
	Data    T      `json:"data"`
}

// NewResponse 创建一个通用响应
func NewResponse[T any](code int, message string, data T) Response[T] {
	return Response[T]{
		Code:    code,
		Message: message,
		Data:    data,
	}
}

// Success 创建一个成功响应
func Success[T any](message string, data T) Response[T] {
	return NewResponse(0, message, data)
}

// 空数据结构体
type EmptyObject struct{}

// SuccessNoData 创建一个没有数据的成功响应
func SuccessNoData(message string) Response[EmptyObject] {
	return Success(message, EmptyObject{})
}

// Error 创建一个错误响应
func Error(code int, message string) Response[any] {
	var nilData any
	return NewResponse(code, message, nilData)
}

// ErrorWithDetails 创建带有详细错误信息的响应
func ErrorWithDetails(code int, message string, details interface{}) Response[any] {
	detailedMessage := message
	var nilData any

	// 根据不同类型的details，将信息拼接到message中
	switch d := details.(type) {
	case map[string]string:
		// 如果是字符串映射（常见的字段验证错误）
		if len(d) > 0 {
			errorDetails := ""
			for field, errMsg := range d {
				if errorDetails != "" {
					errorDetails += "; "
				}
				errorDetails += field + ": " + errMsg
			}
			detailedMessage = message + " (" + errorDetails + ")"
		}
	case map[string]interface{}:
		// 如果是通用映射
		if len(d) > 0 {
			errorDetails := ""
			for field, errMsg := range d {
				if errorDetails != "" {
					errorDetails += "; "
				}
				errorDetails += field + ": " + fmt.Sprintf("%v", errMsg)
			}
			detailedMessage = message + " (" + errorDetails + ")"
		}
	case []string:
		// 如果是字符串数组
		if len(d) > 0 {
			detailedMessage = message + " (" + strings.Join(d, "; ") + ")"
		}
	case string:
		// 如果是单个字符串
		if d != "" {
			detailedMessage = message + " (" + d + ")"
		}
	}

	return NewResponse(code, detailedMessage, nilData)
}

// ErrorWithDetailsMap 创建带有映射类型详细错误信息的响应
func ErrorWithDetailsMap(code int, message string, details map[string]string) Response[any] {
	detailedMessage := message
	var nilData any

	if len(details) > 0 {
		errorDetails := ""
		for field, errMsg := range details {
			if errorDetails != "" {
				errorDetails += "; "
			}
			errorDetails += field + ": " + errMsg
		}
		detailedMessage = message + " (" + errorDetails + ")"
	}

	return NewResponse(code, detailedMessage, nilData)
}

// ErrorWithDetailsAnyMap 创建带有任意值映射类型详细错误信息的响应
func ErrorWithDetailsAnyMap(code int, message string, details map[string]any) Response[any] {
	detailedMessage := message
	var nilData any

	if len(details) > 0 {
		errorDetails := ""
		for field, value := range details {
			if errorDetails != "" {
				errorDetails += "; "
			}
			errorDetails += field + ": " + fmt.Sprintf("%v", value)
		}
		detailedMessage = message + " (" + errorDetails + ")"
	}

	return NewResponse(code, detailedMessage, nilData)
}

// ErrorWithDetailsSlice 创建带有切片类型详细错误信息的响应
func ErrorWithDetailsSlice(code int, message string, details []string) Response[any] {
	detailedMessage := message
	var nilData any

	if len(details) > 0 {
		detailedMessage = message + " (" + strings.Join(details, "; ") + ")"
	}

	return NewResponse(code, detailedMessage, nilData)
}

//------------------------- 直接使用上下文的响应函数 -------------------------

// SuccessJSON 返回成功响应（HTTP状态码固定为200）
func SuccessJSON[T any](ctx *gin.Context, message string, data T) {
	ctx.JSON(http.StatusOK, Success(message, data))
}

// SuccessNoDataJSON 返回没有数据的成功响应
func SuccessNoDataJSON(ctx *gin.Context, message string) {
	ctx.JSON(http.StatusOK, SuccessNoData(message))
}

// ErrorJSON 返回错误响应（HTTP状态码由外部传入）
func ErrorJSON(ctx *gin.Context, httpStatus int, code int, message string) {
	ctx.JSON(httpStatus, Error(code, message))
}

// ErrorWithDetailsJSON 返回带详细信息的错误响应
func ErrorWithDetailsJSON(ctx *gin.Context, httpStatus int, code int, message string, errors interface{}) {
	// 根据不同类型的errors调用相应的方法
	switch e := errors.(type) {
	case map[string]string:
		ErrorWithDetailsMapJSON(ctx, httpStatus, code, message, e)
	case map[string]any:
		ErrorWithDetailsAnyMapJSON(ctx, httpStatus, code, message, e)
	case []string:
		ErrorWithDetailsSliceJSON(ctx, httpStatus, code, message, e)
	case string:
		ErrorJSON(ctx, httpStatus, code, message+" ("+e+")")
	default:
		// 对于其他类型，尝试转换为字符串
		ErrorJSON(ctx, httpStatus, code, message+" ("+fmt.Sprintf("%v", errors)+")")
	}
}

// BadRequest 返回400错误响应
func BadRequest(ctx *gin.Context, message string) {
	ErrorJSON(ctx, http.StatusBadRequest, 400, message)
}

// Unauthorized 返回401错误响应
func Unauthorized(ctx *gin.Context, message string) {
	ErrorJSON(ctx, http.StatusUnauthorized, 401, message)
}

// NotFound 返回404错误响应
func NotFound(ctx *gin.Context, message string) {
	ErrorJSON(ctx, http.StatusNotFound, 404, message)
}

// ServerError 返回500错误响应
func ServerError(ctx *gin.Context, message string) {
	ErrorJSON(ctx, http.StatusInternalServerError, 500, message)
}

// ErrorWithDetailsMapJSON 返回带有映射类型详细错误信息的JSON响应
func ErrorWithDetailsMapJSON(ctx *gin.Context, httpStatus int, code int, message string, details map[string]string) {
	ctx.JSON(httpStatus, ErrorWithDetailsMap(code, message, details))
}

// ErrorWithDetailsAnyMapJSON 返回带有任意值映射类型详细错误信息的JSON响应
func ErrorWithDetailsAnyMapJSON(ctx *gin.Context, httpStatus int, code int, message string, details map[string]any) {
	ctx.JSON(httpStatus, ErrorWithDetailsAnyMap(code, message, details))
}

// ErrorWithDetailsSliceJSON 返回带有切片类型详细错误信息的JSON响应
func ErrorWithDetailsSliceJSON(ctx *gin.Context, httpStatus int, code int, message string, details []string) {
	ctx.JSON(httpStatus, ErrorWithDetailsSlice(code, message, details))
}
