package vo

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	HotSearch           []string                     `json:"hot_search" example:"京东e卡,携程,微信立减金,支付宝消费券" label:"热门搜索关键词"`
	Notice              string                       `json:"notice" example:"【京顺回收】160余种卡券回收，如需了解更多问题，请点击咨询人工客服：09:00-24:00                                        24小时在线回收卡券，卡券提交后所有交易均可自动处理、24小时均可提现，1-3分钟内立即到账！" label:"系统公告"`
	HomeRecommendations []HomeRecommendationResponse `json:"home_recommendations" label:"首页推荐列表"`
	IcpNumber           string                       `json:"icp_number" example:"湘ICP备2023018441号-2X" label:"备案号"`
	HeroImageURL        string                       `json:"hero_image_url" example:"https://example.com/public/system/666.jpg" label:"Hero图链接"`
}

// HomeRecommendationResponse 首页推荐响应
type HomeRecommendationResponse struct {
	ID          uint   `json:"id" example:"1"`                                    // 推荐ID
	ImageURL    string `json:"image_url" example:"https://example.com/image.jpg"` // 图片链接
	Title       string `json:"title" example:"热门推荐标题"`                           // 文案标题
	JumpURL     string `json:"jump_url" example:"https://example.com/jump"`       // 跳转链接
	Description string `json:"description" example:"推荐描述信息"`                     // 描述
	Sort        int    `json:"sort" example:"1"`                                  // 排序
}

// RecentOrderResponse 最近订单响应
type RecentOrderResponse struct {
	FaceValue  float64 `json:"face_value" example:"1000"`                      // 面值
	CreateTime string  `json:"create_time" example:"2025-07-30 21:34:54"`      // 创建时间
	Msg        string  `json:"msg" example:"陈**提交了1000京东e卡-快销"`               // 消息
	PName      string  `json:"pname" example:"京东e卡-快销"`                       // 产品名称
}

// RecentOrderListResponse 最近订单列表响应
type RecentOrderListResponse struct {
	List []RecentOrderResponse `json:"list"` // 最近订单列表
}

// BankListResponse 银行列表响应
type BankListResponse struct {
	Banks []string `json:"banks" example:"中国银行,工商银行,农业银行,建设银行" label:"银行列表"`
}
