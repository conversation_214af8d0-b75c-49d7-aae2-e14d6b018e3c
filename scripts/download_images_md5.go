package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"recycle-server/internal/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	CDN_BASE_URL = "https://cdn.img.weichewl.com/"
	DOWNLOAD_DIR = "/Users/<USER>/mod/recycle-server/public/system"
)

// 计算文件内容的MD5值
func calculateMD5(data []byte) string {
	hash := md5.Sum(data)
	return hex.EncodeToString(hash[:])
}

// 获取文件扩展名
func getFileExtension(url string) string {
	// 从URL中提取文件扩展名
	parts := strings.Split(url, ".")
	if len(parts) > 1 {
		ext := strings.ToLower(parts[len(parts)-1])
		// 只保留常见的图片扩展名
		if ext == "jpg" || ext == "jpeg" || ext == "png" || ext == "gif" || ext == "webp" {
			return "." + ext
		}
	}
	return ".png" // 默认扩展名
}

// 下载图片并返回MD5文件名
func downloadImageWithMD5(url, savePath string) (string, error) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	
	// 发送GET请求
	resp, err := client.Get(url)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()
	
	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP状态错误: %d", resp.StatusCode)
	}
	
	// 读取文件内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取内容失败: %v", err)
	}
	
	// 计算MD5值
	md5Hash := calculateMD5(data)
	
	// 获取文件扩展名
	ext := getFileExtension(url)
	
	// 生成新的文件名
	newFileName := md5Hash + ext
	newFilePath := filepath.Join(filepath.Dir(savePath), newFileName)
	
	// 检查文件是否已存在
	if _, err := os.Stat(newFilePath); err == nil {
		return newFileName, nil // 文件已存在，直接返回文件名
	}
	
	// 确保目录存在
	dir := filepath.Dir(newFilePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}
	
	// 保存文件
	if err := os.WriteFile(newFilePath, data, 0644); err != nil {
		return "", fmt.Errorf("保存文件失败: %v", err)
	}
	
	return newFileName, nil
}

// 处理图片URL并下载
func processImageURLWithMD5(imageURL string, productID uint, imageType string, logger *zap.Logger) (string, error) {
	if imageURL == "" {
		return "", nil // 跳过空URL
	}
	
	// 拼接完整URL
	fullURL := CDN_BASE_URL + imageURL
	
	// 构建保存路径（临时路径，实际文件名会根据MD5生成）
	tempPath := filepath.Join(DOWNLOAD_DIR, "temp.png")
	
	// 下载图片并获取MD5文件名
	logger.Info("开始下载图片", 
		zap.String("url", fullURL),
		zap.Uint("product_id", productID),
		zap.String("type", imageType))
	
	fileName, err := downloadImageWithMD5(fullURL, tempPath)
	if err != nil {
		return "", fmt.Errorf("下载图片失败 [%s]: %v", fullURL, err)
	}
	
	logger.Info("图片下载成功", 
		zap.String("file", fileName),
		zap.Uint("product_id", productID),
		zap.String("type", imageType))
	
	// 返回相对路径格式
	return "public/system/" + fileName, nil
}

// 获取所有产品并下载图片，同时更新数据库
func downloadAndUpdateProductImages(db *gorm.DB, logger *zap.Logger) error {
	var products []models.Product
	
	// 查询所有产品
	if err := db.Find(&products).Error; err != nil {
		return fmt.Errorf("查询产品失败: %v", err)
	}
	
	logger.Info("开始下载产品图片", zap.Int("total_products", len(products)))
	
	successCount := 0
	errorCount := 0
	
	for i, product := range products {
		logger.Info("处理产品", 
			zap.Int("current", i+1),
			zap.Int("total", len(products)),
			zap.Uint("product_id", product.ID),
			zap.String("product_name", product.Name))
		
		needUpdate := false
		updatedProduct := product
		
		// 下载图片1
		if product.ImageURL1 != "" {
			newPath, err := processImageURLWithMD5(product.ImageURL1, product.ID, "img1", logger)
			if err != nil {
				logger.Error("下载图片1失败", 
					zap.Uint("product_id", product.ID),
					zap.String("image_url", product.ImageURL1),
					zap.Error(err))
				errorCount++
			} else if newPath != "" {
				updatedProduct.ImageURL1 = newPath
				needUpdate = true
				successCount++
			}
		}
		
		// 下载图片2
		if product.ImageURL2 != "" {
			newPath, err := processImageURLWithMD5(product.ImageURL2, product.ID, "img2", logger)
			if err != nil {
				logger.Error("下载图片2失败", 
					zap.Uint("product_id", product.ID),
					zap.String("image_url", product.ImageURL2),
					zap.Error(err))
				errorCount++
			} else if newPath != "" {
				updatedProduct.ImageURL2 = newPath
				needUpdate = true
				successCount++
			}
		}
		
		// 更新数据库
		if needUpdate {
			if err := db.Model(&product).Updates(map[string]interface{}{
				"image_url1": updatedProduct.ImageURL1,
				"image_url2": updatedProduct.ImageURL2,
			}).Error; err != nil {
				logger.Error("更新产品失败", 
					zap.Uint("product_id", product.ID),
					zap.Error(err))
				errorCount++
			} else {
				logger.Info("产品更新成功", 
					zap.Uint("product_id", product.ID),
					zap.String("new_img1", updatedProduct.ImageURL1),
					zap.String("new_img2", updatedProduct.ImageURL2))
			}
		}
		
		// 避免请求过于频繁
		time.Sleep(100 * time.Millisecond)
	}
	
	logger.Info("图片下载和更新完成", 
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount),
		zap.Int("total_products", len(products)))
	
	return nil
}

func main() {
	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		fmt.Printf("初始化脚本基础失败: %v\n", err)
		os.Exit(1)
	}
	defer scriptBase.Close()

	scriptBase.Info("产品图片下载和MD5重命名脚本开始执行")

	// 检查下载目录
	if err := os.MkdirAll(DOWNLOAD_DIR, 0755); err != nil {
		scriptBase.Error("创建下载目录失败", zap.Error(err))
		os.Exit(1)
	}
	
	scriptBase.Info("下载目录已准备", zap.String("path", DOWNLOAD_DIR))

	// 获取数据库连接
	db := scriptBase.ScriptService.GetDB()

	// 开始下载图片并更新数据库
	if err := downloadAndUpdateProductImages(db, scriptBase.ScriptService.GetLogger()); err != nil {
		scriptBase.Error("下载图片失败", zap.Error(err))
		os.Exit(1)
	}

	scriptBase.Info("产品图片下载和MD5重命名脚本执行完成")
}
