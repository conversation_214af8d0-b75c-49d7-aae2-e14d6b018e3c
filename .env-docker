APP_NAME=京海回收
APP_ENV=development
APP_DEBUG=true
APP_HOST=0.0.0.0
APP_PORT=8084

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=**************
DB_PORT=30006
DB_DATABASE=recycle_server
DB_USERNAME=root
DB_PASSWORD=95175300h

# 数据库高级配置
DB_CHARSET=utf8mb4
DB_PARSE_TIME=true
DB_TIME_ZONE=Asia%2FShanghai
DB_DEFAULT_STRING_SIZE=255
DB_DISABLE_DATETIME_PRECISION=true
DB_SKIP_INITIALIZE_WITH_VERSION=false
DB_AUTO_MIGRATE=false
DB_SLOW_SQL=500
DB_LOG_LEVEL=debug
DB_IGNORE_RECORD_NOT_FOUND_ERROR=true
DB_MAX_IDLE_CONN=10
DB_MAX_OPEN_CONN=100
DB_CONN_MAX_LIFETIME=1
DB_CONN_MAX_IDLE_TIME=1

# GORM配置
DB_GORM_SKIP_DEFAULT_TX=false
DB_GORM_TABLE_PREFIX=
DB_GORM_SINGULAR_TABLE=true
DB_GORM_COVER_LOGGER=true
DB_GORM_PREPARE_STMT=false
DB_GORM_CLOSE_FOREIGN_KEY=true

# JWT配置
JWT_SECRET=SGcCvhV6lStn65xfRWN1qjJ2F2dx95uVufQeahwk2fM=
JWT_TTL=864000

# 日志配置
LOG_LEVEL=debug
LOG_CHANNEL=console

# Swagger配置
SWAGGER_ENABLE=true

# Redis配置
REDIS_HOST=**************
REDIS_PORT=30379
REDIS_PASSWORD=95175300h
REDIS_DB=19

# 阿里云短信配置
SMS_ACCESS_KEY_ID=LTAI5tL9qVfa9V5Vxu4nzGtf
SMS_ACCESS_KEY_SECRET=******************************
SMS_LOGIN_TEMPLATE_CODE=SMS_464090595
SMS_SIGN_NAME=易企合财务
SMS_ENDPOINT=dysmsapi.aliyuncs.com

# 微信支付配置
WECHAT_PAY_APP_ID=wxe9872dc9df660e7b
WECHAT_PAY_MCH_ID=1718394860
WECHAT_PAY_PLATFORM_CERTS=/Users/<USER>/mod/recycle-server/cert/cert.pem
WECHAT_PAY_PRIVATE_KEY=/Users/<USER>/mod/recycle-server/cert/apiclient_key.pem
WECHAT_PAY_SECRET_KEY=61236027705e48ef3ff7f71f156a7137
WECHAT_PAY_CERTIFICATE=/Users/<USER>/mod/recycle-server/cert/apiclient_cert.pem
WECHAT_PAY_NOTIFY_URL=https://resume.avrilko.com/api/openapi/wechatpay





# 支付宝配置
ALI_PAY_APP_ID=2021005106664115
ALI_PAY_APP_SECRET_CERT=/Users/<USER>/mod/recycle-server/cert/alicert/alipay_app_private_key.pem
ALI_PAY_APP_PUBLIC_CERT_PATH=/Users/<USER>/mod/recycle-server/cert/alicert/appCertPublicKey_2021005106664115.crt
ALI_PAY_PUBLIC_CERT_PATH=/Users/<USER>/mod/recycle-server/cert/alicert/alipayCertPublicKey_RSA2.crt
ALI_PAY_ROOT_CERT_PATH=/Users/<USER>/mod/recycle-server/cert/alicert/alipayRootCert.crt
ALI_PAY_NOTIFY_URL=https://resume.avrilko.com/api/openapi/alipay
ALI_PAY_IS_PRODUCTION=true





# 后端域名配置
BACKEND_DOMAIN=http://**************:8084
