package services

import (
	"context"

	"mime/multipart"

	"go.uber.org/zap"
)

// UploadService 上传服务接口
type UploadService interface {
	// UploadAvatar 上传头像
	UploadAvatar(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error)
}

// uploadService 上传服务实现
type uploadService struct {
	logger *zap.Logger
}

// NewUploadService 创建上传服务
func NewUploadService(logger *zap.Logger) UploadService {
	return &uploadService{
		logger: logger,
	}
}

// UploadAvatar 上传头像
func (s *uploadService) UploadAvatar(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error) {
	// TODO: 实现头像上传逻辑 (OSS服务未实现)
	s.logger.Info("UploadAvatar called", zap.String("filename", file.Filename))

	// 临时返回
	return "http://example.com/avatar.jpg", file.Filename, nil
}
