package vo

import (
	"time"

	"recycle-server/internal/enum"
)

// MessageResponse 消息响应视图对象
type MessageResponse struct {
	ID          uint                   `json:"id" example:"1"`
	Title       string                 `json:"title" example:"系统维护通知"`
	Description string                 `json:"description" example:"系统将于今晚进行维护，预计维护时间为2小时"`
	MessageType enum.SystemMessageType `json:"message_type" example:"1"` // 消息类型 1:系统消息 2:账号消息
	UserID      uint                   `json:"user_id" example:"1"`      // 用户ID
	CreatedAt   time.Time              `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt   time.Time              `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// MessageListResponse 消息列表响应视图对象
type MessageListResponse struct {
	List []MessageResponse `json:"list"`
}
