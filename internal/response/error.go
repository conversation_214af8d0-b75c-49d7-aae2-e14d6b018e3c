package response

import (
	"github.com/gin-gonic/gin"
)

// ThrowError 在控制器中抛出错误的统一函数，可处理任何错误类型
// 使用方式: return response.ThrowError(c, err)
// 示例:
//   - return response.ThrowError(c, errors.New("标准错误"))
//   - return response.ThrowError(c, exception.ErrUserNotFound)
//   - return response.ThrowError(c, exception.ErrUserNotFound.WithDetail("用户ID:123"))
func ThrowError(c *gin.Context, err error) error {
	// 添加错误到上下文
	c.Error(err)
	// 立即中断后续处理
	c.Abort()
	return err
}
