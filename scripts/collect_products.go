package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"recycle-server/internal/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// API请求结构
type APIRequest struct {
	Type  int    `json:"type"`
	Token string `json:"token"`
	T     int64  `json:"t"`
	Sign  string `json:"sign"`
}

// API响应结构
type APIResponse struct {
	Code int           `json:"code"`
	Msg  string        `json:"msg"`
	Data []ProductData `json:"data"`
}

// 产品数据结构
type ProductData struct {
	ID          int     `json:"id"`
	Type        int     `json:"type"`
	CardImg     string  `json:"CardImg"`
	CardImg2    string  `json:"CardImg2"`
	CardNoRule  *string `json:"CardNoRule"` // 可能为null
	CardPwdRule string  `json:"CardPwdRule"`
	PCode       string  `json:"pcode"`
	PName       string  `json:"pname"`
	Guize       string  `json:"guize"`
	Guize2      string  `json:"guize2"`
	Demo        string  `json:"demo"`
	ZhekouTips  string  `json:"zhekouTips"`
	CLSJ        string  `json:"clsj"`
	Sort        int     `json:"sort"`
	Status      int     `json:"status"`
	IsDelete    int     `json:"isdelete"`
	CreateTime  int64   `json:"create_time"`
	UpdateTime  int64   `json:"update_time"`
	Qudao       int     `json:"qudao"`
	OrderModel  int     `json:"order_model"`
	NeedNo      int     `json:"need_no"`
	NeedNoAdd   int     `json:"need_no_add"`
}

// 生成MD5签名 - 按照JavaScript中的signMD5算法
func generateSign(productType int, timestamp int64) string {
	// 按照键名排序：t, token, type
	// 拼接对应的值：timestamp + "" + productType + "wzxcx"
	signStr := fmt.Sprintf("%d%d", timestamp, productType) + "wzxcx"

	h := md5.New()
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// 发送API请求
func fetchProductsByType(productType int) ([]ProductData, error) {
	url := "https://www.jingshun-wl.com//index/api/getProductByType"

	// 生成时间戳和签名
	timestamp := time.Now().UnixMilli()
	sign := generateSign(productType, timestamp)

	// 构建请求数据
	requestData := APIRequest{
		Type:  productType,
		Token: "",
		T:     timestamp,
		Sign:  sign,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", "Hm_lvt_7d819bdc9092a7bff549425eea604695=**********; Hm_lpvt_7d819bdc9092a7bff549425eea604695=**********; HMACCOUNT=C8B0B67D925E4E8E; 53gid2=**************; visitor_type=new; 53gid0=**************; 53gid1=**************; 53revisit=*************; 53kf_72704241_from_host=www.jingshun-wl.com; 53kf_72704241_keyword=https%3A%2F%2Fwww.baidu.com%2F; uuid_53kf_72704241=7a4159c5bda2b6dbd8293133f6e28673; 53kf_72704241_land_page=https%253A%252F%252Fwww.jingshun-wl.com%252F; kf_72704241_land_page_ok=1; 53uvid=1; onliner_zdfq72704241=0")
	req.Header.Set("Origin", "https://www.jingshun-wl.com")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Priority", "u=1, i")
	req.Header.Set("Referer", "https://www.jingshun-wl.com/h5/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp APIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("API返回错误: %s", apiResp.Msg)
	}

	return apiResp.Data, nil
}

// 转换并保存产品数据
func saveProducts(db *gorm.DB, products []ProductData, categoryID uint) error {
	for _, productData := range products {
		// 处理可能为null的CardNoRule
		cardNoRule := ""
		if productData.CardNoRule != nil {
			cardNoRule = *productData.CardNoRule
		}

		// 转换为数据库模型
		product := models.Product{
			ID:           uint(productData.ID), // API的id映射到数据库的id
			Name:         productData.PName,
			CategoryID:   categoryID,
			ImageURL1:    productData.CardImg,
			ImageURL2:    productData.CardImg2,
			CardNoRule:   cardNoRule,
			CardPwdRule:  productData.CardPwdRule,
			WriteOffDesc: productData.CLSJ,
			Demo:         productData.Demo,
			RuleTip:      productData.Guize,
			RuleDesc:     productData.Guize2,
			DiscountTip:  productData.ZhekouTips,
			Sort:         productData.Sort,
		}

		// 检查产品是否已存在（根据名称和分类ID）
		var existingProduct models.Product
		result := db.Where("name = ? AND category_id = ?", product.Name, product.CategoryID).First(&existingProduct)

		if result.Error == gorm.ErrRecordNotFound {
			// 产品不存在，创建新产品
			if err := db.Create(&product).Error; err != nil {
				return fmt.Errorf("创建产品失败: %v", err)
			}
		} else if result.Error == nil {
			// 产品已存在，更新产品信息
			if err := db.Model(&existingProduct).Updates(&product).Error; err != nil {
				return fmt.Errorf("更新产品失败: %v", err)
			}
		} else {
			return fmt.Errorf("查询产品失败: %v", result.Error)
		}
	}

	return nil
}

func main() {
	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		log.Fatalf("初始化脚本基础失败: %v", err)
	}
	defer scriptBase.Close()

	scriptBase.Info("产品数据采集脚本开始执行")

	// 获取数据库连接
	db := scriptBase.ScriptService.GetDB()

	// 采集类型1到6的产品数据
	for productType := 1; productType <= 6; productType++ {
		scriptBase.Info("开始采集产品数据", zap.Int("type", productType))

		// 获取产品数据
		products, err := fetchProductsByType(productType)
		if err != nil {
			scriptBase.Error("获取产品数据失败",
				zap.Int("type", productType),
				zap.Error(err))
			continue
		}

		scriptBase.Info("获取到产品数据",
			zap.Int("type", productType),
			zap.Int("count", len(products)))

		// 保存产品数据
		if err := saveProducts(db, products, uint(productType)); err != nil {
			scriptBase.Error("保存产品数据失败",
				zap.Int("type", productType),
				zap.Error(err))
			continue
		}

		scriptBase.Info("产品数据保存成功",
			zap.Int("type", productType),
			zap.Int("count", len(products)))

		// 避免请求过于频繁
		time.Sleep(1 * time.Second)
	}

	scriptBase.Info("产品数据采集脚本执行完成")
}
