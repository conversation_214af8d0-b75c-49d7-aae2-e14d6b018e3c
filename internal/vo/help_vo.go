package vo

// HelpResponse 帮助信息响应
type HelpResponse struct {
	ID          uint   `json:"id" example:"1"`                                    // 帮助ID
	Title       string `json:"title" example:"平台是如何结算,结算时间是多久?"`                   // 标题
	Description string `json:"description" example:"平台现在支持微信、支付宝、网银提现..."`         // 描述
	ImageURL    string `json:"image_url" example:"https://example.com/help.jpg"` // 图片链接
	Sort        int    `json:"sort" example:"1"`                                  // 排序
}

// HelpListResponse 帮助信息列表响应
type HelpListResponse struct {
	List []HelpResponse `json:"list"` // 帮助信息列表
}
