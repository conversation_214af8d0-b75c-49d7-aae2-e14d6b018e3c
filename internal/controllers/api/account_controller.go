package api

import (
	"recycle-server/internal/dto"
	"recycle-server/internal/models"
	"recycle-server/internal/response"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
)

// AccountController 账户控制器
type AccountController struct {
	accountService services.AccountService
}

// NewAccountController 创建账户控制器
func NewAccountController(accountService services.AccountService) *AccountController {
	return &AccountController{
		accountService: accountService,
	}
}

// AddAlipayAccount 添加支付宝账户
// @Summary 添加支付宝账户
// @Description 为当前登录用户添加支付宝账户
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Param request body dto.AddAlipayAccountRequest true "添加支付宝账户请求"
// @Success 200 {object} vo.SuccessAPIResponse "添加成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/alipay [post]
func (c *AccountController) AddAlipayAccount(ctx *gin.Context, req dto.AddAlipayAccountRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层添加支付宝账户
	err := c.accountService.AddAlipayAccount(user.ID, req.AlipayAccount)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "支付宝账户添加成功")
}

// DeleteAlipayAccount 删除支付宝账户
// @Summary 删除支付宝账户
// @Description 删除当前登录用户的指定支付宝账户，需要验证交易密码
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Param id path int true "支付宝账户ID"
// @Param request body dto.DeleteAlipayAccountRequest true "删除支付宝账户请求"
// @Success 200 {object} vo.SuccessAPIResponse "删除成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误或交易密码错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "账户不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/alipay/{id} [delete]
func (c *AccountController) DeleteAlipayAccount(ctx *gin.Context, req dto.DeleteAlipayAccountRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除支付宝账户
	err := c.accountService.DeleteAlipayAccount(user.ID, req.ID, req.TradePassword)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "支付宝账户删除成功")
}

// GetAllAlipayAccounts 获取所有支付宝账户
// @Summary 获取所有支付宝账户
// @Description 获取当前登录用户的所有支付宝账户
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.AlipayAccountListResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/alipay [get]
func (c *AccountController) GetAllAlipayAccounts(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取所有支付宝账户
	result, err := c.accountService.GetAllAlipayAccounts(user.ID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}

// AddBankCard 添加银行卡
// @Summary 添加银行卡
// @Description 为当前登录用户添加银行卡
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Param request body dto.AddBankCardRequest true "添加银行卡请求"
// @Success 200 {object} vo.SuccessAPIResponse "添加成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/bank-card [post]
func (c *AccountController) AddBankCard(ctx *gin.Context, req dto.AddBankCardRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层添加银行卡
	err := c.accountService.AddBankCard(user.ID, req.BankAccount, req.BankName)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "银行卡添加成功")
}

// DeleteBankCard 删除银行卡
// @Summary 删除银行卡
// @Description 删除当前登录用户的指定银行卡，需要验证交易密码
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Param id path int true "银行卡ID"
// @Param request body dto.DeleteBankCardRequest true "删除银行卡请求"
// @Success 200 {object} vo.SuccessAPIResponse "删除成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误或交易密码错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "银行卡不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/bank-card/{id} [delete]
func (c *AccountController) DeleteBankCard(ctx *gin.Context, req dto.DeleteBankCardRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除银行卡
	err := c.accountService.DeleteBankCard(user.ID, req.ID, req.TradePassword)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "银行卡删除成功")
}

// GetAllBankCards 获取所有银行卡
// @Summary 获取所有银行卡
// @Description 获取当前登录用户的所有银行卡
// @Tags API/账户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.BankCardListResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/accounts/bank-card [get]
func (c *AccountController) GetAllBankCards(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层获取所有银行卡
	result, err := c.accountService.GetAllBankCards(user.ID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", result)
}
