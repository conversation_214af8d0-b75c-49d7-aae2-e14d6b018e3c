package services

import (
	"recycle-server/internal/enum"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MessageService 消息服务接口
type MessageService interface {
	// GetMessagesByType 根据消息类型获取消息列表（支持分页）
	GetMessagesByType(userID uint, messageType enum.SystemMessageType, page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error)
}

// messageService 消息服务实现
type messageService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewMessageService 创建消息服务
func NewMessageService(db *gorm.DB, logger *zap.Logger) MessageService {
	return &messageService{
		db:     db,
		logger: logger,
	}
}

// GetMessagesByType 根据消息类型获取消息列表（支持分页）
func (s *messageService) GetMessagesByType(userID uint, messageType enum.SystemMessageType, page, pageSize int) (*vo.PaginatedList[vo.MessageResponse], error) {
	var messages []models.Message
	var total int64

	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	// 构建查询条件
	query := s.db.Model(&models.Message{}).Where("message_type = ?", messageType)
	
	// 如果userID不为0，则只查询该用户的消息或系统消息（userID为0）
	if userID != 0 {
		query = query.Where("user_id = ? OR user_id = 0", userID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		s.logger.Error("查询消息总数失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 获取分页数据
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&messages).Error; err != nil {
		s.logger.Error("查询消息列表失败", zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应格式
	var messageResponses []vo.MessageResponse
	for _, message := range messages {
		messageResponses = append(messageResponses, vo.MessageResponse{
			ID:          message.ID,
			Title:       message.Title,
			Description: message.Description,
			MessageType: message.MessageType,
			UserID:      message.UserID,
			CreatedAt:   message.CreatedAt,
			UpdatedAt:   message.UpdatedAt,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if messageResponses == nil {
		messageResponses = []vo.MessageResponse{}
	}

	// 创建分页响应
	paginatedList := vo.NewPaginatedList(messageResponses, total, page, pageSize)
	return &paginatedList, nil
}


