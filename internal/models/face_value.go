package models

// FaceValue 面值表模型
type FaceValue struct {
	ID        uint    `json:"id" gorm:"primaryKey"`
	ProductID uint    `json:"product_id" gorm:"not null;default:0;index;comment:产品ID"`
	Name      string  `json:"name" gorm:"size:100;not null;default:'';comment:面值名称"`
	Value     float64 `json:"value" gorm:"type:decimal(10,2);not null;default:0;comment:面值"`
	Status    bool    `json:"status" gorm:"not null;default:true;comment:状态 true:启用 false:禁用"`
	MyMaxRate string  `json:"my_max_rate" gorm:"size:20;not null;default:'';comment:我的最大费率"`
	MyMinRate string  `json:"my_min_rate" gorm:"size:20;not null;default:'';comment:我的最小费率"`
	MyRate    string  `json:"my_rate" gorm:"size:20;not null;default:'';comment:我的费率"`

	// 关联产品
	Product Product `json:"product" gorm:"foreignKey:ProductID;references:ID"`

	Base
}

// TableName 指定表名
func (FaceValue) TableName() string {
	return "face_value"
}
