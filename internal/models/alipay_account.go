package models

// AlipayAccount 支付宝账户模型
type AlipayAccount struct {
	ID            uint   `json:"id" gorm:"primaryKey"`
	UserID        uint   `json:"user_id" gorm:"not null;index;default:0;comment:用户ID"`
	AlipayAccount string `json:"alipay_account" gorm:"size:100;not null;default:'';comment:支付宝账号"`

	Base

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID;references:ID"`
}

// TableName 指定表名
func (AlipayAccount) TableName() string {
	return "alipay_account"
}
