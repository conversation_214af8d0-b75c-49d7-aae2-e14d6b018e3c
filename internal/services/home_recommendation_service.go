package services

import (
	"strings"

	"recycle-server/config"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HomeRecommendationService 首页推荐服务接口
type HomeRecommendationService interface {
	// GetActiveRecommendations 获取所有启用的首页推荐
	GetActiveRecommendations() ([]vo.HomeRecommendationResponse, error)
}

// homeRecommendationService 首页推荐服务实现
type homeRecommendationService struct {
	db     *gorm.DB
	logger *zap.Logger
	config *config.Config
}

// NewHomeRecommendationService 创建首页推荐服务
func NewHomeRecommendationService(db *gorm.DB, logger *zap.Logger, cfg *config.Config) HomeRecommendationService {
	return &homeRecommendationService{
		db:     db,
		logger: logger,
		config: cfg,
	}
}

// GetActiveRecommendations 获取所有启用的首页推荐
func (s *homeRecommendationService) GetActiveRecommendations() ([]vo.HomeRecommendationResponse, error) {
	var recommendations []models.HomeRecommendation

	// 查询所有启用的推荐，按排序字段升序排列
	if err := s.db.Where("is_active = ?", true).Order("sort ASC, id ASC").Find(&recommendations).Error; err != nil {
		s.logger.Error("查询首页推荐失败", zap.Error(err))
		return nil, exception.ErrHelpQueryFailed // 复用现有的错误类型
	}

	// 转换为响应格式
	var responses []vo.HomeRecommendationResponse
	for _, rec := range recommendations {
		// 拼接后端域名到图片URL
		imageURL := s.buildImageURL(rec.ImageURL)

		responses = append(responses, vo.HomeRecommendationResponse{
			ID:          rec.ID,
			ImageURL:    imageURL,
			Title:       rec.Title,
			JumpURL:     rec.JumpURL,
			Description: rec.Description,
			Sort:        rec.Sort,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if responses == nil {
		responses = []vo.HomeRecommendationResponse{}
	}

	s.logger.Info("查询首页推荐成功", zap.Int("count", len(responses)))

	return responses, nil
}

// buildImageURL 构建完整的图片URL
func (s *homeRecommendationService) buildImageURL(imageURL string) string {
	// 如果图片URL为空，返回空字符串
	if imageURL == "" {
		return ""
	}

	// 如果图片URL已经是完整的URL（包含http://或https://），直接返回
	if strings.HasPrefix(imageURL, "http://") || strings.HasPrefix(imageURL, "https://") {
		return imageURL
	}

	// 如果后端域名配置为空，直接返回原URL
	if s.config.BackendDomain == "" {
		return imageURL
	}

	// 确保后端域名不以/结尾，图片URL以/开头
	backendDomain := strings.TrimSuffix(s.config.BackendDomain, "/")
	if !strings.HasPrefix(imageURL, "/") {
		imageURL = "/" + imageURL
	}

	return backendDomain + imageURL
}
