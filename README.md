# 京海回收服务器

一个基于 Go 语言开发的现代化回收服务平台后端系统，提供用户管理、文件上传、支付集成等功能。

## 🚀 特性

- **用户系统**: 手机号验证码登录/注册，用户信息管理
- **文件上传**: 支持头像上传，集成云存储
- **支付集成**: 支持微信支付和支付宝支付
- **短信服务**: 集成阿里云短信服务
- **API 文档**: 集成 Swagger 自动生成 API 文档
- **依赖注入**: 使用 Google Wire 进行依赖管理
- **定时任务**: 支持 Cron 定时任务
- **缓存支持**: Redis 缓存集成
- **日志系统**: 结构化日志记录

## 📋 技术栈

- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **ORM**: GORM
- **依赖注入**: Google Wire
- **API 文档**: Swagger/OpenAPI 3.0
- **日志**: Zap
- **配置管理**: Viper
- **定时任务**: Cron
- **支付**: 微信支付 v3 API、支付宝 SDK
- **短信**: 阿里云短信服务

## 🛠️ 快速开始

### 环境要求

- Go 1.21 或更高版本
- MySQL 8.0+
- Redis 6.0+
- Git

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/avrilko-go/recycle-server.git
cd recycle-server

# 安装 Go 依赖
go mod tidy

# 安装开发工具
go install github.com/google/wire/cmd/wire@latest
go install github.com/swaggo/swag/cmd/swag@latest
```

### 配置环境

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置数据库、Redis、支付等信息：
```bash
# 应用配置
APP_NAME=京海回收
APP_ENV=development
APP_DEBUG=true
APP_PORT=8084

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=recycle_server

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT 配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h

# 阿里云短信配置
SMS_ACCESS_KEY_ID=your_access_key_id
SMS_ACCESS_KEY_SECRET=your_access_key_secret
SMS_LOGIN_TEMPLATE_CODE=your_template_code
SMS_SIGN_NAME=your_sign_name
SMS_ENDPOINT=dysmsapi.aliyuncs.com
```

### 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE recycle_server CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 运行数据库迁移（应用启动时自动执行）
```

### 生成依赖注入代码

```bash
wire ./internal/wire
```

### 生成 API 文档

```bash
swag init
```

### 启动服务

```bash
# 开发模式
go run main.go

# 或者编译后运行
go build -o recycle-server
./recycle-server
```

服务启动后访问：
- **API 服务**: http://localhost:8084
- **API 文档**: http://localhost:8084/swagger/index.html

## 📁 项目结构

```
recycle-server/
├── cmd/                    # 命令行工具
├── config/                 # 配置文件
├── docs/                   # Swagger 生成的文档
├── internal/               # 内部代码
│   ├── controllers/        # 控制器层
│   │   └── api/           # API 控制器
│   ├── cron/              # 定时任务
│   ├── dto/               # 数据传输对象
│   ├── enum/              # 枚举定义
│   ├── exception/         # 异常定义
│   ├── middleware/        # 中间件
│   ├── models/            # 数据模型
│   ├── pkg/               # 基础包
│   ├── repository/        # 数据访问层
│   ├── routes/            # 路由定义
│   │   └── api/          # API 路由
│   ├── services/          # 业务逻辑层
│   ├── utils/             # 工具函数
│   ├── vo/                # 视图对象
│   └── wire/              # 依赖注入
├── scripts/               # 脚本文件
├── cert/                  # 证书文件
├── .env                   # 环境配置
├── .env.example          # 环境配置示例
├── go.mod                # Go 模块文件
├── go.sum                # Go 依赖校验
└── main.go               # 程序入口
```

## 🔌 API 接口

### 认证管理
- `POST /api/auth/login-code` - 验证码登录
- `POST /api/auth/sms/code` - 发送短信验证码

### 用户管理
- `GET /api/users/info` - 获取当前用户信息
- `PUT /api/users/username` - 修改用户名
- `PUT /api/users/avatar` - 修改用户头像
- `POST /api/users/logout` - 退出登录

### 文件上传
- `POST /api/upload/avatar` - 上传头像

详细的 API 文档请访问 Swagger UI: http://localhost:8084/swagger/index.html

## 🔧 开发指南

### 添加新的 API 接口

1. 在 `internal/dto/` 中定义请求和响应结构体
2. 在 `internal/controllers/api/` 中添加控制器方法
3. 在 `internal/routes/api/` 中注册路由
4. 在 `internal/services/` 中实现业务逻辑
5. 重新生成 Swagger 文档: `swag init`

### 数据库迁移

项目使用 GORM 的 AutoMigrate 功能，在应用启动时自动创建和更新表结构。

### 依赖注入

使用 Google Wire 进行依赖注入管理：

```bash
# 修改依赖关系后重新生成
wire ./internal/wire
```

## 🚀 部署

### Docker 部署

```bash
# 构建镜像
docker build -t recycle-server .

# 运行容器
docker run -d \
  --name recycle-server \
  -p 8084:8084 \
  --env-file .env-docker \
  recycle-server
```

### 生产环境配置

1. 设置环境变量 `APP_ENV=production`
2. 配置生产数据库和 Redis
3. 设置强密码和密钥
4. 配置 HTTPS 证书
5. 设置日志级别和输出

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **作者**: avrilko
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/avrilko-go/recycle-server

## 🙏 致谢

感谢以下开源项目：

- [Gin](https://github.com/gin-gonic/gin) - HTTP Web 框架
- [GORM](https://github.com/go-gorm/gorm) - ORM 库
- [Wire](https://github.com/google/wire) - 依赖注入
- [Swagger](https://github.com/swaggo/swag) - API 文档生成
- [Zap](https://github.com/uber-go/zap) - 日志库