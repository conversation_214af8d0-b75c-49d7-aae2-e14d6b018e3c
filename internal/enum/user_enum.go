package enum

// UserType 用户类型
type UserType int

const (
	UserTypeRegular UserType = 1 // 普通用户
	UserTypeVIP     UserType = 2 // 会员
)

// UserStatus 用户状态
type UserStatus int

const (
	UserStatusEnabled  UserStatus = 1 // 启用
	UserStatusDisabled UserStatus = 2 // 禁用
)

// String 返回用户状态的字符串表示
func (s UserStatus) String() string {
	switch s {
	case UserStatusEnabled:
		return "已启用"
	case UserStatusDisabled:
		return "已禁用"
	default:
		return "未知状态"
	}
}

// PaymentStatus 支付状态
type PaymentStatus int

const (
	PaymentStatusPending PaymentStatus = 1 // 待支付
	PaymentStatusPaid    PaymentStatus = 2 // 已支付
	PaymentStatusTimeout PaymentStatus = 3 // 支付超时
	PaymentStatusFailed  PaymentStatus = 4 // 支付失败
)

// MembershipStatus 会员状态
type MembershipStatus int

const (
	MembershipStatusActive  MembershipStatus = 1 // 生效中
	MembershipStatusExpired MembershipStatus = 2 // 已过期
)

// AuthType 认证类型
type AuthType int

const (
	AuthTypeNone     AuthType = 1 // 未认证
	AuthTypeBasic    AuthType = 2 // 普通认证
	AuthTypeAdvanced AuthType = 3 // 高级认证
)
