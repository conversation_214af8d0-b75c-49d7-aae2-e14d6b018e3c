package models

// SystemMessage 系统消息模型
type SystemMessage struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Title       string `json:"title" gorm:"size:255;not null;comment:消息标题"`
	Description string `json:"description" gorm:"type:text;comment:消息描述"`
	Type        int    `json:"type" gorm:"type:tinyint(1);default:1;index;comment:消息类型 1:系统通知 2:活动消息 3:维护公告"`
	Status      int    `json:"status" gorm:"type:tinyint(1);default:1;index;comment:状态 1:启用 2:禁用"`
	Priority    int    `json:"priority" gorm:"type:tinyint(1);default:1;comment:优先级 1:低 2:中 3:高"`
	IsRead      bool   `json:"is_read" gorm:"type:tinyint(1);default:0;comment:是否已读 0:未读 1:已读"`

	Base
}

// TableName 指定表名
func (SystemMessage) TableName() string {
	return "system_message"
}
