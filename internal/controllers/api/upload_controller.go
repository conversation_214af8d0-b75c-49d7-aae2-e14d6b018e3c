package api

import (
	"recycle-server/internal/dto"
	"recycle-server/internal/response"
	"recycle-server/internal/services"
	"recycle-server/internal/vo"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// UploadController 上传控制器
type UploadController struct {
	uploadService services.UploadService
	logger        *zap.Logger
}

// NewUploadController 创建上传控制器
func NewUploadController(uploadService services.UploadService, logger *zap.Logger) *UploadController {
	return &UploadController{
		uploadService: uploadService,
		logger:        logger,
	}
}

// UploadAvatar 上传头像
// @Summary 上传头像
// @Description 上传用户头像，支持jpg、png、webp格式，最大7MB
// @Tags API/文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UploadResponse} "上传成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/upload/avatar [post]
func (c *UploadController) UploadAvatar(ctx *gin.Context, req dto.UploadAvatarRequest) {
	// 获取文件
	file := req.File

	// 调用上传服务上传头像
	fileURL, originalFilename, err := c.uploadService.UploadAvatar(ctx, file)
	if err != nil {
		c.logger.Error("上传头像失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回上传结果
	response.SuccessJSON(ctx, "上传成功", vo.UploadResponse{
		URL:      fileURL,
		Filename: originalFilename,
	})
}
