package main

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"time"

	"recycle-server/internal/models"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// API请求结构 - 面值详情
type FaceValueAPIRequest struct {
	PID   string `json:"pid"`
	Token string `json:"token"`
	T     int64  `json:"t"`
	Sign  string `json:"sign"`
}

// API响应结构 - 面值详情
type FaceValueAPIResponse struct {
	Code int               `json:"code"`
	Msg  string            `json:"msg"`
	Data []FaceValueData  `json:"data"`
}

// 面值数据结构
type FaceValueData struct {
	ID        int    `json:"id"`
	FaceValue string `json:"FaceValue"`
	Name      string `json:"name"`
	MyMaxRate string `json:"myMaxRate"`
	MyMinRate string `json:"myMinRate"`
	MyRate    string `json:"myRate"`
	Status    int    `json:"status"`
	Type      int    `json:"type"`
}

// 生成MD5签名 - 面值详情接口
func generateFaceValueSign(pid string, timestamp int64) string {
	// 按照键名排序：pid, sign, t, token
	// 拼接对应的值：pid + "" + timestamp + "wzxcx"
	signStr := pid + "" + fmt.Sprintf("%d", timestamp) + "wzxcx"

	h := md5.New()
	h.Write([]byte(signStr))
	return hex.EncodeToString(h.Sum(nil))
}

// 发送API请求获取面值详情
func fetchFaceValuesByPID(pid string) ([]FaceValueData, error) {
	url := "https://www.jingshun-wl.com//index/api/getProductDeatil"

	// 生成时间戳和签名
	timestamp := time.Now().UnixMilli()
	sign := generateFaceValueSign(pid, timestamp)

	// 构建请求数据
	requestData := FaceValueAPIRequest{
		PID:   pid,
		Token: "",
		T:     timestamp,
		Sign:  sign,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cookie", "Hm_lvt_7d819bdc9092a7bff549425eea604695=**********; Hm_lpvt_7d819bdc9092a7bff549425eea604695=**********; HMACCOUNT=C8B0B67D925E4E8E; 53gid2=**************; visitor_type=new; 53gid0=**************; 53gid1=**************; 53revisit=*************; 53kf_72704241_from_host=www.jingshun-wl.com; 53kf_72704241_keyword=https%3A%2F%2Fwww.baidu.com%2F; uuid_53kf_72704241=7a4159c5bda2b6dbd8293133f6e28673; 53kf_72704241_land_page=https%253A%252F%252Fwww.jingshun-wl.com%252F; kf_72704241_land_page_ok=1; 53uvid=1; onliner_zdfq72704241=0")
	req.Header.Set("Origin", "https://www.jingshun-wl.com")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Priority", "u=1, i")
	req.Header.Set("Referer", "https://www.jingshun-wl.com/h5/")
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var apiResp FaceValueAPIResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if apiResp.Code != 0 {
		return nil, fmt.Errorf("API返回错误: %s", apiResp.Msg)
	}

	return apiResp.Data, nil
}

// 转换并保存面值数据
func saveFaceValues(db *gorm.DB, faceValues []FaceValueData, productID uint) error {
	for _, faceValueData := range faceValues {
		// 解析面值
		faceValue, err := strconv.ParseFloat(faceValueData.FaceValue, 64)
		if err != nil {
			// 如果解析失败，设置为0
			faceValue = 0
		}

		// 转换状态为bool
		status := faceValueData.Status == 1

		// 转换为数据库模型
		faceValueModel := models.FaceValue{
			ID:        uint(faceValueData.ID), // API的id映射到数据库的id
			ProductID: productID,
			Name:      faceValueData.Name,
			Value:     faceValue,
			Status:    status,
			MyMaxRate: faceValueData.MyMaxRate,
			MyMinRate: faceValueData.MyMinRate,
			MyRate:    faceValueData.MyRate,
		}

		// 检查面值是否已存在（根据ID）
		var existingFaceValue models.FaceValue
		result := db.Where("id = ?", faceValueModel.ID).First(&existingFaceValue)

		if result.Error == gorm.ErrRecordNotFound {
			// 面值不存在，创建新面值
			if err := db.Create(&faceValueModel).Error; err != nil {
				return fmt.Errorf("创建面值失败: %v", err)
			}
		} else if result.Error == nil {
			// 面值已存在，更新面值信息
			if err := db.Model(&existingFaceValue).Updates(&faceValueModel).Error; err != nil {
				return fmt.Errorf("更新面值失败: %v", err)
			}
		} else {
			return fmt.Errorf("查询面值失败: %v", result.Error)
		}
	}

	return nil
}

// 获取所有产品ID
func getAllProductIDs(db *gorm.DB) ([]uint, error) {
	var productIDs []uint
	err := db.Model(&models.Product{}).Pluck("id", &productIDs).Error
	if err != nil {
		return nil, fmt.Errorf("查询产品ID失败: %v", err)
	}
	return productIDs, nil
}

func main() {
	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		log.Fatalf("初始化脚本基础失败: %v", err)
	}
	defer scriptBase.Close()

	scriptBase.Info("面值数据采集脚本开始执行")

	// 获取数据库连接
	db := scriptBase.ScriptService.GetDB()

	// 获取所有产品ID
	productIDs, err := getAllProductIDs(db)
	if err != nil {
		scriptBase.Error("获取产品ID失败", zap.Error(err))
		return
	}

	scriptBase.Info("获取到产品列表", zap.Int("count", len(productIDs)))

	// 遍历每个产品ID，采集面值数据
	successCount := 0
	errorCount := 0

	for _, productID := range productIDs {
		scriptBase.Info("开始采集产品面值数据", zap.Uint("product_id", productID))

		// 获取面值数据
		faceValues, err := fetchFaceValuesByPID(fmt.Sprintf("%d", productID))
		if err != nil {
			scriptBase.Error("获取面值数据失败",
				zap.Uint("product_id", productID),
				zap.Error(err))
			errorCount++
			continue
		}

		scriptBase.Info("获取到面值数据",
			zap.Uint("product_id", productID),
			zap.Int("count", len(faceValues)))

		// 保存面值数据
		if err := saveFaceValues(db, faceValues, productID); err != nil {
			scriptBase.Error("保存面值数据失败",
				zap.Uint("product_id", productID),
				zap.Error(err))
			errorCount++
			continue
		}

		scriptBase.Info("面值数据保存成功",
			zap.Uint("product_id", productID),
			zap.Int("count", len(faceValues)))

		successCount++

		// 避免请求过于频繁
		time.Sleep(500 * time.Millisecond)
	}

	scriptBase.Info("面值数据采集脚本执行完成",
		zap.Int("total_products", len(productIDs)),
		zap.Int("success_count", successCount),
		zap.Int("error_count", errorCount))
}
