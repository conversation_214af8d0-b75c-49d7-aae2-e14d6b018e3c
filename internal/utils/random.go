package utils

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"
)

// GenerateRandomCode 生成指定长度的随机数字验证码
// 参数 length: 验证码长度
// 返回值: 生成的随机数字验证码字符串
func GenerateRandomCode(length int) string {
	// 使用时间戳作为种子
	rand.New(rand.NewSource(time.Now().UnixNano()))

	// 确保第一位不是0
	firstDigit := rand.Intn(9) + 1
	code := strconv.Itoa(firstDigit)

	// 生成剩余位数
	for i := 1; i < length; i++ {
		code += strconv.Itoa(rand.Intn(10))
	}

	return code
}

// GenerateRandomNickname 生成随机昵称，格式为"形容词的名词"
func GenerateRandomNickname() (string, error) {
	// 常用形容词列表
	adjectives := []string{
		"温柔", "可爱", "勇敢", "聪明", "快乐",
		"活泼", "机智", "优雅", "善良", "开朗",
		"敏捷", "灵动", "稳重", "睿智", "坚强",
		"热情", "沉稳", "耐心", "诚实", "幽默",
		"阳光", "清新", "文艺", "梦幻", "雅致",
		"柔和", "淡雅", "从容", "安静", "自信",
		"细腻", "率真", "朴实", "浪漫", "神秘",
		"儒雅", "调皮", "风趣", "坦率", "随性",
		"谦和", "勤劳", "甜美", "精致", "高雅",
		"俏皮", "秀气", "干净", "典雅", "单纯",
		"执着", "认真", "爽朗", "慷慨", "含蓄",
		"理性", "正直", "豁达", "大方", "天真",
	}

	// 常用名词列表
	nouns := []string{
		"山羊", "狮子", "猫咪", "小鹿", "飞鸟",
		"海豚", "蝴蝶", "狐狸", "熊猫", "老虎",
		"河马", "松鼠", "鲸鱼", "猫头鹰", "小象",
		"蜻蜓", "花朵", "星星", "流云", "微风",
		"橡树", "溪流", "山峰", "海浪", "月亮",
		"兔子", "孔雀", "青蛙", "蜜蜂", "鹦鹉",
		"企鹅", "树懒", "浣熊", "长颈鹿", "小熊",
		"金鱼", "蚂蚁", "蜗牛", "萤火虫", "独角兽",
		"樱花", "竹子", "梅花", "向日葵", "雪花",
		"云朵", "瀑布", "湖泊", "森林", "草原",
		"彩虹", "极光", "大海", "沙漠", "冰川",
		"宇宙", "银河", "流星", "夕阳", "雨滴",
	}

	// 使用时间戳作为种子
	source := rand.NewSource(time.Now().UnixNano())
	random := rand.New(source)

	// 随机选择形容词和名词
	adj := adjectives[random.Intn(len(adjectives))]
	noun := nouns[random.Intn(len(nouns))]

	// 组合成"形容词的名词"格式
	return fmt.Sprintf("%s的%s", adj, noun), nil
}

// GenerateRandomAvatar 生成随机头像URL
func GenerateRandomAvatar() string {
	// 生成0-16之间的随机数
	avatarIndex := rand.Intn(17)
	return fmt.Sprintf("https://cdn.pandaresume.com/avatar/avataaars-%d.svg", avatarIndex)
}

// GenerateGenderBasedAvatar 根据性别生成头像URL
func GenerateGenderBasedAvatar(gender string) string {
	// 生成1-14之间的随机数
	avatarIndex := rand.Intn(14) + 1

	// 根据性别判断，支持多种性别表示方式
	switch gender {
	case "男", "男性", "male", "Male", "MALE", "M", "m":
		return fmt.Sprintf("https://cdn.pandaresume.com/rand_avatar/man/%d.jpg", avatarIndex)
	case "女", "女性", "female", "Female", "FEMALE", "F", "f":
		return fmt.Sprintf("https://cdn.pandaresume.com/rand_avatar/woman/%d.jpg", avatarIndex)
	default:
		// 如果性别不明确，随机选择男女头像
		if rand.Intn(2) == 0 {
			return fmt.Sprintf("https://cdn.pandaresume.com/rand_avatar/man/%d.jpg", avatarIndex)
		} else {
			return fmt.Sprintf("https://cdn.pandaresume.com/rand_avatar/woman/%d.jpg", avatarIndex)
		}
	}
}

// GenerateFallbackUsername 生成备用用户名（当随机昵称生成失败时使用）
func GenerateFallbackUsername() string {
	// 生成0-999999之间的随机数
	randomNum := rand.Intn(999999)
	return fmt.Sprintf("用户%d", randomNum)
}
