package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"recycle-server/config"
	"recycle-server/internal/pkg"
	"recycle-server/internal/wire"

	"go.uber.org/zap"
)

// ScriptBase 脚本基础结构，提供统一的依赖注入入口点
type ScriptBase struct {
	ScriptService wire.ScriptService
	Context       context.Context
}

// NewScriptBase 创建脚本基础实例
func NewScriptBase() (*ScriptBase, error) {
	// 设置时区为东八区（Asia/Shanghai）
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return nil, fmt.Errorf("加载时区失败: %v", err)
	}
	time.Local = loc

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %v", err)
	}

	// 初始化日志
	if err := pkg.InitLogger(cfg); err != nil {
		return nil, fmt.Errorf("初始化日志失败: %v", err)
	}

	// 初始化验证器
	if err := pkg.InitValidator(); err != nil {
		return nil, fmt.Errorf("初始化验证器失败: %v", err)
	}

	// 初始化数据库（如果不是跳过数据库模式）
	skipDBInit := os.Getenv("SKIP_DB_INIT") == "true"
	if !skipDBInit {
		if err := pkg.InitDB(cfg); err != nil {
			return nil, fmt.Errorf("初始化数据库失败: %v", err)
		}

		// 根据配置决定是否自动迁移数据库
		if cfg.Database.AutoMigrate {
			if err := pkg.AutoMigrateDB(); err != nil {
				pkg.Warn("自动迁移数据库失败", zap.Error(err))
			}
		} else if cfg.App.Env == "development" && cfg.App.Debug {
			// 如果是开发环境且开启了Debug模式，也可以自动迁移
			if err := pkg.AutoMigrateDB(); err != nil {
				pkg.Warn("自动迁移数据库失败", zap.Error(err))
			}
		}
	}

	// 初始化Redis
	if err := pkg.InitRedis(cfg); err != nil {
		return nil, fmt.Errorf("初始化Redis失败: %v", err)
	}

	// 构建脚本服务
	scriptService, err := wire.BuildScriptService()
	if err != nil {
		return nil, fmt.Errorf("构建脚本服务失败: %v", err)
	}

	return &ScriptBase{
		ScriptService: scriptService,
		Context:       context.Background(),
	}, nil
}

// Close 清理资源
func (sb *ScriptBase) Close() {
	// 关闭Redis连接
	if err := pkg.CloseRedis(); err != nil {
		log.Printf("关闭Redis连接失败: %v", err)
	}

	// 同步日志
	if pkg.Logger != nil {
		pkg.Logger.Sync()
	}
}

// Info 记录信息日志
func (sb *ScriptBase) Info(msg string, fields ...zap.Field) {
	if sb.ScriptService != nil && sb.ScriptService.GetLogger() != nil {
		sb.ScriptService.GetLogger().Info(msg, fields...)
	} else {
		log.Println("INFO:", msg)
	}
}

// Error 记录错误日志
func (sb *ScriptBase) Error(msg string, fields ...zap.Field) {
	if sb.ScriptService != nil && sb.ScriptService.GetLogger() != nil {
		sb.ScriptService.GetLogger().Error(msg, fields...)
	} else {
		log.Println("ERROR:", msg)
	}
}

// Debug 记录调试日志
func (sb *ScriptBase) Debug(msg string, fields ...zap.Field) {
	if sb.ScriptService != nil && sb.ScriptService.GetLogger() != nil {
		sb.ScriptService.GetLogger().Debug(msg, fields...)
	} else {
		log.Println("DEBUG:", msg)
	}
}

// Warn 记录警告日志
func (sb *ScriptBase) Warn(msg string, fields ...zap.Field) {
	if sb.ScriptService != nil && sb.ScriptService.GetLogger() != nil {
		sb.ScriptService.GetLogger().Warn(msg, fields...)
	} else {
		log.Println("WARN:", msg)
	}
}

// Fatal 记录致命错误日志并退出
func (sb *ScriptBase) Fatal(msg string, fields ...zap.Field) {
	if sb.ScriptService != nil && sb.ScriptService.GetLogger() != nil {
		sb.ScriptService.GetLogger().Fatal(msg, fields...)
	} else {
		log.Fatal("FATAL:", msg)
	}
}

// GetScriptService 获取脚本服务，通过它可以访问所有其他服务
func (sb *ScriptBase) GetScriptService() wire.ScriptService {
	return sb.ScriptService
}
