package vo

// UploadResponse 上传响应视图对象
type UploadResponse struct {
	URL      string `json:"url" example:"https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg"` // 文件URL
	Filename string `json:"filename" example:"123456789.jpg"`                                     // 文件名
}

// ParseFileResponse 文件解析响应视图对象
type ParseFileResponse struct {
	Content  string `json:"content" example:"这是解析后的文件内容..."` // 解析后的文件内容
	Filename string `json:"filename" example:"document.pdf"` // 原始文件名
	FileSize int64  `json:"file_size" example:"1024000"`     // 文件大小（字节）
	FileType string `json:"file_type" example:"pdf"`         // 文件类型
}
