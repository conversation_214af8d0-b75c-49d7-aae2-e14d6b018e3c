package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RegisterUploadRoutes 注册上传相关路由
func RegisterUploadRoutes(
	router *gin.RouterGroup,
	uploadController *api.UploadController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 上传路由组
	uploadGroup := router.Group("/upload")

	// 需要认证的上传路由组，使用JWT用户认证中间件
	uploadGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 上传头像
		uploadGroup.POST("/avatar", middleware.WithValidation(uploadController.UploadAvatar))
	}
}
