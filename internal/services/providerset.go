package services

import (
	"github.com/google/wire"
)

// CodeServiceSet 验证码服务依赖注入集合
var CodeServiceSet = wire.NewSet(
	NewCodeService,
	wire.Bind(new(SMSCodeService), new(CodeService)),
)

// ServiceSet 服务层依赖注入集合
var ServiceSet = wire.NewSet(
	NewUserService,
	CodeServiceSet,
	NewUploadService,
	NewProductService,
	NewHelpService,
	NewAccountService,
	NewHomeRecommendationService,
	// 未来可以在这里添加其他服务的构造函数
)
