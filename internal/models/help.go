package models

// Help 帮助模型
type Help struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Title       string `json:"title" gorm:"size:200;not null;default:'';comment:标题"`
	Description string `json:"description" gorm:"type:text;comment:描述"`
	ImageURL    string `json:"image_url" gorm:"size:500;default:'';comment:图片链接"`
	Sort        int    `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序字段，数值越小越靠前"`

	Base
}

// TableName 指定表名
func (Help) TableName() string {
	return "help"
}
