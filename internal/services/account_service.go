package services

import (
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/utils"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// AccountService 账户服务接口
type AccountService interface {
	// 支付宝账户相关方法
	AddAlipayAccount(userID uint, alipayAccount string) error
	DeleteAlipayAccount(userID uint, accountID uint, tradePassword string) error
	GetAllAlipayAccounts(userID uint) (*vo.AlipayAccountListResponse, error)

	// 银行卡相关方法
	AddBankCard(userID uint, bankAccount, bankName string) error
	DeleteBankCard(userID uint, cardID uint, tradePassword string) error
	GetAllBankCards(userID uint) (*vo.BankCardListResponse, error)
}

// accountService 账户服务实现
type accountService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewAccountService 创建账户服务
func NewAccountService(db *gorm.DB, logger *zap.Logger) AccountService {
	return &accountService{
		db:     db,
		logger: logger,
	}
}

// AddAlipayAccount 添加支付宝账户
func (s *accountService) AddAlipayAccount(userID uint, alipayAccount string) error {
	// 检查该用户是否已经添加过相同的支付宝账户
	var existingAccount models.AlipayAccount
	err := s.db.Where("user_id = ? AND alipay_account = ?", userID, alipayAccount).First(&existingAccount).Error
	if err == nil {
		s.logger.Warn("支付宝账户已存在", zap.Uint("userID", userID), zap.String("alipayAccount", alipayAccount))
		return exception.ErrAlipayAccountExists
	}

	account := &models.AlipayAccount{
		UserID:        userID,
		AlipayAccount: alipayAccount,
	}

	if err := s.db.Create(account).Error; err != nil {
		s.logger.Error("添加支付宝账户失败", zap.Uint("userID", userID), zap.String("alipayAccount", alipayAccount), zap.Error(err))
		return exception.ErrInternalServer
	}

	s.logger.Info("添加支付宝账户成功", zap.Uint("userID", userID), zap.Uint("accountID", account.ID))
	return nil
}

// DeleteAlipayAccount 删除支付宝账户
func (s *accountService) DeleteAlipayAccount(userID uint, accountID uint, tradePassword string) error {
	// 验证用户交易密码
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		s.logger.Error("获取用户信息失败", zap.Uint("userID", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 验证交易密码
	if !utils.VerifyPassword(user.TradePassword, tradePassword) {
		s.logger.Warn("交易密码错误", zap.Uint("userID", userID))
		return exception.ErrTradePasswordIncorrect
	}

	result := s.db.Where("id = ? AND user_id = ?", accountID, userID).Delete(&models.AlipayAccount{})
	if result.Error != nil {
		s.logger.Error("删除支付宝账户失败", zap.Uint("userID", userID), zap.Uint("accountID", accountID), zap.Error(result.Error))
		return exception.ErrInternalServer
	}

	if result.RowsAffected == 0 {
		s.logger.Warn("支付宝账户不存在或不属于当前用户", zap.Uint("userID", userID), zap.Uint("accountID", accountID))
		return exception.ErrNotFound
	}

	s.logger.Info("删除支付宝账户成功", zap.Uint("userID", userID), zap.Uint("accountID", accountID))
	return nil
}

// GetAllAlipayAccounts 获取用户所有支付宝账户
func (s *accountService) GetAllAlipayAccounts(userID uint) (*vo.AlipayAccountListResponse, error) {
	var accounts []models.AlipayAccount
	if err := s.db.Where("user_id = ?", userID).Order("created_at DESC").Find(&accounts).Error; err != nil {
		s.logger.Error("获取支付宝账户列表失败", zap.Uint("userID", userID), zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应VO
	var accountResponses []vo.AlipayAccountResponse
	for _, account := range accounts {
		accountResponses = append(accountResponses, vo.AlipayAccountResponse{
			ID:            account.ID,
			UserID:        account.UserID,
			AlipayAccount: account.AlipayAccount,
			CreatedAt:     account.CreatedAt,
			UpdatedAt:     account.UpdatedAt,
		})
	}

	return &vo.AlipayAccountListResponse{
		List: accountResponses,
	}, nil
}

// AddBankCard 添加银行卡
func (s *accountService) AddBankCard(userID uint, bankAccount, bankName string) error {
	// 检查该用户是否已经添加过相同的银行卡
	var existingCard models.BankCard
	err := s.db.Where("user_id = ? AND bank_account = ?", userID, bankAccount).First(&existingCard).Error
	if err == nil {
		s.logger.Warn("银行卡已存在", zap.Uint("userID", userID), zap.String("bankAccount", bankAccount))
		return exception.ErrBankCardExists
	}

	card := &models.BankCard{
		UserID:      userID,
		BankAccount: bankAccount,
		BankName:    bankName,
	}

	if err := s.db.Create(card).Error; err != nil {
		s.logger.Error("添加银行卡失败", zap.Uint("userID", userID), zap.String("bankAccount", bankAccount), zap.Error(err))
		return exception.ErrInternalServer
	}

	s.logger.Info("添加银行卡成功", zap.Uint("userID", userID), zap.Uint("cardID", card.ID))
	return nil
}

// DeleteBankCard 删除银行卡
func (s *accountService) DeleteBankCard(userID uint, cardID uint, tradePassword string) error {
	// 验证用户交易密码
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		s.logger.Error("获取用户信息失败", zap.Uint("userID", userID), zap.Error(err))
		return exception.ErrInternalServer
	}

	// 验证交易密码
	if !utils.VerifyPassword(user.TradePassword, tradePassword) {
		s.logger.Warn("交易密码错误", zap.Uint("userID", userID))
		return exception.ErrTradePasswordIncorrect
	}

	result := s.db.Where("id = ? AND user_id = ?", cardID, userID).Delete(&models.BankCard{})
	if result.Error != nil {
		s.logger.Error("删除银行卡失败", zap.Uint("userID", userID), zap.Uint("cardID", cardID), zap.Error(result.Error))
		return exception.ErrInternalServer
	}

	if result.RowsAffected == 0 {
		s.logger.Warn("银行卡不存在或不属于当前用户", zap.Uint("userID", userID), zap.Uint("cardID", cardID))
		return exception.ErrNotFound
	}

	s.logger.Info("删除银行卡成功", zap.Uint("userID", userID), zap.Uint("cardID", cardID))
	return nil
}

// GetAllBankCards 获取用户所有银行卡
func (s *accountService) GetAllBankCards(userID uint) (*vo.BankCardListResponse, error) {
	var cards []models.BankCard
	if err := s.db.Where("user_id = ?", userID).Order("created_at DESC").Find(&cards).Error; err != nil {
		s.logger.Error("获取银行卡列表失败", zap.Uint("userID", userID), zap.Error(err))
		return nil, exception.ErrInternalServer
	}

	// 转换为响应VO
	var cardResponses []vo.BankCardResponse
	for _, card := range cards {
		cardResponses = append(cardResponses, vo.BankCardResponse{
			ID:          card.ID,
			UserID:      card.UserID,
			BankAccount: card.BankAccount,
			BankName:    card.BankName,
			CreatedAt:   card.CreatedAt,
			UpdatedAt:   card.UpdatedAt,
		})
	}

	return &vo.BankCardListResponse{
		List: cardResponses,
	}, nil
}
