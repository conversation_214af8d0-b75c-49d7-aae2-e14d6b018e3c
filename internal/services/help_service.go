package services

import (
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/vo"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// HelpService 帮助服务接口
type HelpService interface {
	// GetAllHelps 获取所有帮助信息
	GetAllHelps() (*vo.HelpListResponse, error)
}

// helpService 帮助服务实现
type helpService struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewHelpService 创建帮助服务
func NewHelpService(db *gorm.DB, logger *zap.Logger) HelpService {
	return &helpService{
		db:     db,
		logger: logger,
	}
}

// GetAllHelps 获取所有帮助信息
func (s *helpService) GetAllHelps() (*vo.HelpListResponse, error) {
	var helps []models.Help

	// 查询所有帮助信息，按排序字段升序排列
	if err := s.db.Order("sort ASC, id ASC").Find(&helps).Error; err != nil {
		s.logger.Error("查询帮助信息失败", zap.Error(err))
		return nil, exception.ErrHelpQueryFailed
	}

	// 转换为响应格式
	var helpResponses []vo.HelpResponse
	for _, help := range helps {
		helpResponses = append(helpResponses, vo.HelpResponse{
			ID:          help.ID,
			Title:       help.Title,
			Description: help.Description,
			ImageURL:    help.ImageURL,
			Sort:        help.Sort,
		})
	}

	// 如果没有结果，设置为空数组而不是nil
	if helpResponses == nil {
		helpResponses = []vo.HelpResponse{}
	}

	s.logger.Info("查询帮助信息成功", zap.Int("count", len(helpResponses)))

	return &vo.HelpListResponse{
		List: helpResponses,
	}, nil
}
