package routes

import (
	"recycle-server/config"
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/pkg"
	"recycle-server/internal/repository"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// RouteInitializer 路由初始化接口
type RouteInitializer interface {
	// Initialize 初始化路由
	Initialize(engine *gin.Engine)
}

// Router 路由结构体
type Router struct {
	cfg               *config.Config
	userController    *api.UserController
	authController    *api.AuthController
	uploadController  *api.UploadController
	productController *api.ProductController
	commonController  *api.CommonController
	accountController *api.AccountController
	jwtService        pkg.JWTService
	userService       services.UserService
	userRepo          repository.UserRepository
	redisClient       *redis.Client
}

// NewRouter 创建路由结构体
func NewRouter(
	cfg *config.Config,
	userController *api.UserController,
	authController *api.AuthController,
	uploadController *api.UploadController,
	productController *api.ProductController,
	commonController *api.CommonController,
	accountController *api.AccountController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) *Router {
	return &Router{
		cfg:               cfg,
		userController:    userController,
		authController:    authController,
		uploadController:  uploadController,
		productController: productController,
		commonController:  commonController,
		accountController: accountController,
		jwtService:        jwtService,
		userService:       userService,
		userRepo:          userRepo,
		redisClient:       redisClient,
	}
}

// Initialize 实现RouteInitializer接口
func (r *Router) Initialize(engine *gin.Engine) {
	InitRoutes(engine, r.cfg, r.userController, r.authController, r.uploadController, r.productController, r.commonController, r.accountController, r.jwtService, r.userService, r.userRepo, r.redisClient)
}
