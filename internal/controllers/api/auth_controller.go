package api

import (
	"recycle-server/config"
	"recycle-server/internal/dto"
	"recycle-server/internal/pkg"
	"recycle-server/internal/response"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	userService services.UserService
	codeService services.CodeService
	jwtService  pkg.JWTService
	config      *config.Config
}

// NewAuthController 创建认证控制器
func NewAuthController(
	userService services.UserService,
	codeService services.CodeService,
	jwtService pkg.JWTService,
	cfg *config.Config,
) *AuthController {
	return &AuthController{
		userService: userService,
		codeService: codeService,
		jwtService:  jwtService,
		config:      cfg,
	}
}

// LoginCode 验证码登录
// @Summary 验证码登录
// @Description 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.LoginCodeRequest true "登录信息"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.TokenResponse} "登录成功，返回token"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "验证码错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/login-code [post]
func (c *AuthController) LoginCode(ctx *gin.Context, req dto.LoginCodeRequest) {
	// 使用验证码登录并生成token
	tokenResponse, err := c.userService.LoginByCodeWithToken(ctx, req.Phone, req.Code)
	if err != nil {
		// 服务层已经记录了错误日志，这里直接返回
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "登录成功", tokenResponse)
}

// SendSMSCode 发送短信验证码
// @Summary 发送短信验证码
// @Description 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
// @Tags API/认证管理
// @Accept json
// @Produce json
// @Param request body dto.SendSMSCodeRequest true "发送验证码请求"
// @Success 200 {object} vo.SuccessAPIResponse "发送成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 429 {object} vo.ErrorAPIResponse "发送频率限制"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/auth/sms/code [post]
func (c *AuthController) SendSMSCode(ctx *gin.Context, req dto.SendSMSCodeRequest) {
	// 调用验证码服务发送验证码
	err := c.codeService.SendSMSCode(ctx, req.Phone)
	if err != nil {
		// 直接将服务层的错误抛出
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "验证码发送成功")
}
