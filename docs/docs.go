// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "API Support",
            "url": "https://github.com/avrilko-go/recycle-server",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/accounts/alipay": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户的所有支付宝账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "获取所有支付宝账户",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.AlipayAccountListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "为当前登录用户添加支付宝账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "添加支付宝账户",
                "parameters": [
                    {
                        "description": "添加支付宝账户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AddAlipayAccountRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/accounts/alipay/{id}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "删除当前登录用户的指定支付宝账户，需要验证交易密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "删除支付宝账户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "支付宝账户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "删除支付宝账户请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.DeleteAlipayAccountRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误或交易密码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "账户不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/accounts/bank-card": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户的所有银行卡",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "获取所有银行卡",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.BankCardListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "为当前登录用户添加银行卡",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "添加银行卡",
                "parameters": [
                    {
                        "description": "添加银行卡请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.AddBankCardRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/accounts/bank-card/{id}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "删除当前登录用户的指定银行卡，需要验证交易密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/账户管理"
                ],
                "summary": "删除银行卡",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "银行卡ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "删除银行卡请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.DeleteBankCardRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误或交易密码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "银行卡不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/login-code": {
            "post": {
                "description": "用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "验证码登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.LoginCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回token",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.TokenResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "验证码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/auth/sms/code": {
            "post": {
                "description": "向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/认证管理"
                ],
                "summary": "发送短信验证码",
                "parameters": [
                    {
                        "description": "发送验证码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.SendSMSCodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "发送成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "429": {
                        "description": "发送频率限制",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/banks": {
            "get": {
                "description": "从系统配置文件中读取所有银行列表信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取所有银行列表",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.BankListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/helps": {
            "get": {
                "description": "获取所有帮助信息，按排序字段升序排列",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取所有帮助信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.HelpListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/recent-orders": {
            "get": {
                "description": "获取最近订单信息，包含面值、时间、消息、产品名称等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取最近订单",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.RecentOrderListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/common/system-config": {
            "get": {
                "description": "获取系统配置信息，包括热门搜索、公告、首页推荐等",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/通用接口"
                ],
                "summary": "获取系统配置",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.SystemConfigResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/categories": {
            "get": {
                "description": "获取所有分类及其下的产品信息，按分类分组返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "获取带分类分组的产品信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/vo.ProductCategoryResponse"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/hot": {
            "get": {
                "description": "获取所有热门产品列表，按排序字段排序返回",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "获取所有热门产品",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.HotProductListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/search": {
            "get": {
                "description": "根据关键词模糊搜索产品名称，返回匹配的产品列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "搜索产品",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keyword",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "搜索成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ProductSearchListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/products/{id}": {
            "get": {
                "description": "根据产品ID获取产品的详细信息，包含面值信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/产品管理"
                ],
                "summary": "根据ID获取产品详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "产品ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.ProductDetailResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "产品不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/upload/avatar": {
            "post": {
                "description": "上传用户头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/文件上传"
                ],
                "summary": "上传头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "上传成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UploadResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/avatar": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户头像",
                "parameters": [
                    {
                        "type": "file",
                        "description": "头像文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "413": {
                        "description": "文件过大",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "415": {
                        "description": "不支持的文件类型",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/delete": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "软删除当前登录用户的账户，注销后无法恢复",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "注销账户",
                "responses": {
                    "200": {
                        "description": "注销成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/info": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "获取当前登录用户信息",
                "responses": {
                    "200": {
                        "description": "获取成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/vo.SuccessAPIResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/vo.UserResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/logout": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "用户退出登录，清除登录状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "退出登录",
                "responses": {
                    "200": {
                        "description": "退出成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/real-name-auth": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "提交真实姓名和身份证号进行实名认证，认证成功后用户认证类型变更为普通认证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "实名认证",
                "parameters": [
                    {
                        "description": "实名认证请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.RealNameAuthRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "认证成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误或已认证",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/trade-password": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的交易密码，需要手机验证码验证",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改交易密码",
                "parameters": [
                    {
                        "description": "修改交易密码请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateTradePasswordRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权或验证码错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        },
        "/api/users/username": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "修改当前登录用户的用户名",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "API/用户管理"
                ],
                "summary": "修改用户名",
                "parameters": [
                    {
                        "description": "修改用户名请求",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dto.UpdateUsernameRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/vo.SuccessAPIResponse"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    },
                    "500": {
                        "description": "服务器内部错误",
                        "schema": {
                            "$ref": "#/definitions/vo.ErrorAPIResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "dto.AddAlipayAccountRequest": {
            "type": "object",
            "required": [
                "alipay_account"
            ],
            "properties": {
                "alipay_account": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1,
                    "example": "***********"
                }
            }
        },
        "dto.AddBankCardRequest": {
            "type": "object",
            "required": [
                "bank_account",
                "bank_name"
            ],
            "properties": {
                "bank_account": {
                    "type": "string",
                    "maxLength": 30,
                    "minLength": 1,
                    "example": "6222021234567890123"
                },
                "bank_name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1,
                    "example": "中国工商银行"
                }
            }
        },
        "dto.DeleteAlipayAccountRequest": {
            "type": "object",
            "required": [
                "id",
                "trade_password"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                },
                "trade_password": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6,
                    "example": "123456"
                }
            }
        },
        "dto.DeleteBankCardRequest": {
            "type": "object",
            "required": [
                "id",
                "trade_password"
            ],
            "properties": {
                "id": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 1
                },
                "trade_password": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6,
                    "example": "123456"
                }
            }
        },
        "dto.LoginCodeRequest": {
            "type": "object",
            "required": [
                "code",
                "phone"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "phone": {
                    "type": "string",
                    "example": "***********"
                }
            }
        },
        "dto.RealNameAuthRequest": {
            "type": "object",
            "required": [
                "id_card",
                "real_name"
            ],
            "properties": {
                "id_card": {
                    "type": "string",
                    "example": "110101199001011234"
                },
                "real_name": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 2,
                    "example": "张三"
                }
            }
        },
        "dto.SendSMSCodeRequest": {
            "type": "object",
            "required": [
                "phone"
            ],
            "properties": {
                "phone": {
                    "type": "string",
                    "example": "***********"
                }
            }
        },
        "dto.UpdateTradePasswordRequest": {
            "type": "object",
            "required": [
                "code",
                "trade_password"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "123456"
                },
                "trade_password": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 6,
                    "example": "123456"
                }
            }
        },
        "dto.UpdateUsernameRequest": {
            "type": "object",
            "required": [
                "username"
            ],
            "properties": {
                "username": {
                    "type": "string",
                    "maxLength": 20,
                    "minLength": 2,
                    "example": "新用户名"
                }
            }
        },
        "enum.AuthType": {
            "type": "integer",
            "enum": [
                1,
                2,
                3
            ],
            "x-enum-comments": {
                "AuthTypeAdvanced": "高级认证",
                "AuthTypeBasic": "普通认证",
                "AuthTypeNone": "未认证"
            },
            "x-enum-varnames": [
                "AuthTypeNone",
                "AuthTypeBasic",
                "AuthTypeAdvanced"
            ]
        },
        "enum.UserType": {
            "type": "integer",
            "enum": [
                1,
                2
            ],
            "x-enum-comments": {
                "UserTypeRegular": "普通用户",
                "UserTypeVIP": "会员"
            },
            "x-enum-varnames": [
                "UserTypeRegular",
                "UserTypeVIP"
            ]
        },
        "vo.AlipayAccountListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.AlipayAccountResponse"
                    }
                }
            }
        },
        "vo.AlipayAccountResponse": {
            "type": "object",
            "properties": {
                "alipay_account": {
                    "type": "string",
                    "example": "***********"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "user_id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.BankCardListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.BankCardResponse"
                    }
                }
            }
        },
        "vo.BankCardResponse": {
            "type": "object",
            "properties": {
                "bank_account": {
                    "type": "string",
                    "example": "6222021234567890123"
                },
                "bank_name": {
                    "type": "string",
                    "example": "中国工商银行"
                },
                "created_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "updated_at": {
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "user_id": {
                    "type": "integer",
                    "example": 1
                }
            }
        },
        "vo.BankListResponse": {
            "type": "object",
            "properties": {
                "banks": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "中国银行",
                        "工商银行",
                        "农业银行",
                        "建设银行"
                    ]
                }
            }
        },
        "vo.CategoryResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "电商卡"
                }
            }
        },
        "vo.ErrorAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 400
                },
                "data": {
                    "description": "响应数据，通常为null"
                },
                "message": {
                    "description": "错误消息",
                    "type": "string",
                    "example": "请求参数错误"
                }
            }
        },
        "vo.FaceValueResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "my_max_rate": {
                    "type": "string",
                    "example": "95.5"
                },
                "my_min_rate": {
                    "type": "string",
                    "example": "94.0"
                },
                "my_rate": {
                    "type": "string",
                    "example": "94.8"
                },
                "name": {
                    "type": "string",
                    "example": "100元"
                },
                "status": {
                    "type": "boolean",
                    "example": true
                },
                "value": {
                    "type": "number",
                    "example": 100
                }
            }
        },
        "vo.HelpListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "帮助信息列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.HelpResponse"
                    }
                }
            }
        },
        "vo.HelpResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string",
                    "example": "平台现在支持微信、支付宝、网银提现..."
                },
                "id": {
                    "description": "帮助ID",
                    "type": "integer",
                    "example": 1
                },
                "image_url": {
                    "description": "图片链接",
                    "type": "string",
                    "example": "https://example.com/help.jpg"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "description": "标题",
                    "type": "string",
                    "example": "平台是如何结算,结算时间是多久?"
                }
            }
        },
        "vo.HomeRecommendationResponse": {
            "type": "object",
            "properties": {
                "description": {
                    "description": "描述",
                    "type": "string",
                    "example": "推荐描述信息"
                },
                "id": {
                    "description": "推荐ID",
                    "type": "integer",
                    "example": 1
                },
                "image_url": {
                    "description": "图片链接",
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "jump_url": {
                    "description": "跳转链接",
                    "type": "string",
                    "example": "https://example.com/jump"
                },
                "sort": {
                    "description": "排序",
                    "type": "integer",
                    "example": 1
                },
                "title": {
                    "description": "文案标题",
                    "type": "string",
                    "example": "热门推荐标题"
                }
            }
        },
        "vo.HotProductListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.HotProductResponse"
                    }
                }
            }
        },
        "vo.HotProductResponse": {
            "type": "object",
            "properties": {
                "discount_tip": {
                    "type": "string",
                    "example": "9.5折起"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                }
            }
        },
        "vo.ProductCategoryResponse": {
            "type": "object",
            "properties": {
                "children": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ProductSummaryResponse"
                    }
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "name": {
                    "type": "string",
                    "example": "电商卡"
                }
            }
        },
        "vo.ProductDetailResponse": {
            "type": "object",
            "properties": {
                "card_no_rule": {
                    "type": "string",
                    "example": "16位数字"
                },
                "card_pwd_rule": {
                    "type": "string",
                    "example": "8位数字"
                },
                "category": {
                    "$ref": "#/definitions/vo.CategoryResponse"
                },
                "category_id": {
                    "type": "integer",
                    "example": 1
                },
                "demo": {
                    "type": "string",
                    "example": "演示内容"
                },
                "discount_tip": {
                    "type": "string",
                    "example": "9.5折起"
                },
                "face_values": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.FaceValueResponse"
                    }
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image1.jpg"
                },
                "image_url2": {
                    "type": "string",
                    "example": "https://example.com/image2.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                },
                "rule_desc": {
                    "type": "string",
                    "example": "详细规则描述"
                },
                "rule_tip": {
                    "type": "string",
                    "example": "规则提示"
                },
                "write_off_desc": {
                    "type": "string",
                    "example": "核销说明"
                }
            }
        },
        "vo.ProductSearchListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.ProductSearchResponse"
                    }
                }
            }
        },
        "vo.ProductSearchResponse": {
            "type": "object",
            "properties": {
                "discount_tip": {
                    "type": "string",
                    "example": "9.5折起"
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                }
            }
        },
        "vo.ProductSummaryResponse": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "image_url1": {
                    "type": "string",
                    "example": "https://example.com/image.jpg"
                },
                "name": {
                    "type": "string",
                    "example": "京东E卡"
                }
            }
        },
        "vo.RecentOrderListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "description": "最近订单列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.RecentOrderResponse"
                    }
                }
            }
        },
        "vo.RecentOrderResponse": {
            "type": "object",
            "properties": {
                "create_time": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2025-07-30 21:34:54"
                },
                "face_value": {
                    "description": "面值",
                    "type": "number",
                    "example": 1000
                },
                "msg": {
                    "description": "消息",
                    "type": "string",
                    "example": "陈**提交了1000京东e卡-快销"
                },
                "pname": {
                    "description": "产品名称",
                    "type": "string",
                    "example": "京东e卡-快销"
                }
            }
        },
        "vo.SuccessAPIResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "业务状态码",
                    "type": "integer",
                    "example": 200
                },
                "data": {
                    "description": "响应数据"
                },
                "message": {
                    "description": "响应消息",
                    "type": "string",
                    "example": "操作成功"
                }
            }
        },
        "vo.SystemConfigResponse": {
            "type": "object",
            "properties": {
                "hero_image_url": {
                    "type": "string",
                    "example": "https://example.com/public/system/666.jpg"
                },
                "home_recommendations": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/vo.HomeRecommendationResponse"
                    }
                },
                "hot_search": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "京东e卡",
                        "携程",
                        "微信立减金",
                        "支付宝消费券"
                    ]
                },
                "icp_number": {
                    "type": "string",
                    "example": "湘ICP备2023018441号-2X"
                },
                "notice": {
                    "type": "string",
                    "example": "【京顺回收】160余种卡券回收，如需了解更多问题，请点击咨询人工客服：09:00-24:00                                        24小时在线回收卡券，卡券提交后所有交易均可自动处理、24小时均可提现，1-3分钟内立即到账！"
                }
            }
        },
        "vo.TokenResponse": {
            "type": "object",
            "properties": {
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                }
            }
        },
        "vo.UploadResponse": {
            "type": "object",
            "properties": {
                "filename": {
                    "description": "文件名",
                    "type": "string",
                    "example": "123456789.jpg"
                },
                "url": {
                    "description": "文件URL",
                    "type": "string",
                    "example": "https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg"
                }
            }
        },
        "vo.UserResponse": {
            "type": "object",
            "properties": {
                "auth_type": {
                    "description": "认证类型 1:未认证 2:普通认证 3:高级认证",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.AuthType"
                        }
                    ],
                    "example": 1
                },
                "avatar": {
                    "type": "string",
                    "example": "http://example.com/avatar.jpg"
                },
                "balance": {
                    "description": "余额",
                    "type": "number",
                    "example": 100.5
                },
                "created_at": {
                    "description": "创建时间",
                    "type": "string",
                    "example": "2023-01-01T12:00:00Z"
                },
                "friend_count": {
                    "description": "好友数",
                    "type": "integer",
                    "example": 10
                },
                "id": {
                    "type": "integer",
                    "example": 1
                },
                "id_card": {
                    "description": "身份证号码",
                    "type": "string",
                    "example": "110101199001011234"
                },
                "open_id": {
                    "type": "string",
                    "example": "oNHwxjgrzgL9H_A2pGLSMuME-X-Q"
                },
                "phone": {
                    "type": "string",
                    "example": "***********"
                },
                "promotion_earning": {
                    "description": "推广收益",
                    "type": "number",
                    "example": 50
                },
                "real_name": {
                    "description": "真实姓名",
                    "type": "string",
                    "example": "张三"
                },
                "user_type": {
                    "description": "用户类型 1:普通用户 2:中级会员 3:高级会员",
                    "allOf": [
                        {
                            "$ref": "#/definitions/enum.UserType"
                        }
                    ],
                    "example": 1
                },
                "username": {
                    "type": "string",
                    "example": "johndoe"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8084",
	BasePath:         "",
	Schemes:          []string{"http", "https"},
	Title:            "京海回收服务器 API",
	Description:      "京海回收服务器 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
