package utils

import (
	"regexp"
	"strconv"
	"strings"
	"time"
)

// ValidateIDCard 验证中国大陆身份证号码
func ValidateIDCard(idCard string) bool {
	// 去除空格
	idCard = strings.TrimSpace(idCard)
	
	// 长度检查：必须是18位
	if len(idCard) != 18 {
		return false
	}
	
	// 格式检查：前17位必须是数字，最后一位可以是数字或X
	matched, _ := regexp.MatchString(`^\d{17}[\dXx]$`, idCard)
	if !matched {
		return false
	}
	
	// 转换为大写
	idCard = strings.ToUpper(idCard)
	
	// 地区码验证（前6位）
	if !validateAreaCode(idCard[:6]) {
		return false
	}
	
	// 出生日期验证（第7-14位）
	if !validateBirthDate(idCard[6:14]) {
		return false
	}
	
	// 校验码验证（最后一位）
	return validateCheckCode(idCard)
}

// validateAreaCode 验证地区码
func validateAreaCode(areaCode string) bool {
	// 简单验证：地区码不能以0开头，且前两位不能是00
	if areaCode[:2] == "00" || areaCode[0] == '0' {
		return false
	}
	
	// 这里可以扩展更详细的地区码验证
	// 为了简化，只做基本验证
	return true
}

// validateBirthDate 验证出生日期
func validateBirthDate(birthDate string) bool {
	// 解析年月日
	year, err := strconv.Atoi(birthDate[:4])
	if err != nil {
		return false
	}
	
	month, err := strconv.Atoi(birthDate[4:6])
	if err != nil {
		return false
	}
	
	day, err := strconv.Atoi(birthDate[6:8])
	if err != nil {
		return false
	}
	
	// 年份范围检查（1900-当前年份）
	currentYear := time.Now().Year()
	if year < 1900 || year > currentYear {
		return false
	}
	
	// 月份检查
	if month < 1 || month > 12 {
		return false
	}
	
	// 日期检查
	if day < 1 || day > 31 {
		return false
	}
	
	// 更精确的日期验证
	date := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC)
	if date.Year() != year || int(date.Month()) != month || date.Day() != day {
		return false
	}
	
	// 不能是未来日期
	if date.After(time.Now()) {
		return false
	}
	
	return true
}

// validateCheckCode 验证校验码
func validateCheckCode(idCard string) bool {
	// 加权因子
	weights := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	
	// 校验码对应表
	checkCodes := []string{"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"}
	
	// 计算校验和
	sum := 0
	for i := 0; i < 17; i++ {
		digit, _ := strconv.Atoi(string(idCard[i]))
		sum += digit * weights[i]
	}
	
	// 计算校验码
	remainder := sum % 11
	expectedCheckCode := checkCodes[remainder]
	
	// 比较校验码
	return string(idCard[17]) == expectedCheckCode
}

// GetGenderFromIDCard 从身份证号码获取性别
func GetGenderFromIDCard(idCard string) string {
	if len(idCard) != 18 {
		return ""
	}
	
	// 第17位数字，奇数为男性，偶数为女性
	genderDigit, err := strconv.Atoi(string(idCard[16]))
	if err != nil {
		return ""
	}
	
	if genderDigit%2 == 1 {
		return "男"
	}
	return "女"
}

// GetBirthDateFromIDCard 从身份证号码获取出生日期
func GetBirthDateFromIDCard(idCard string) (time.Time, error) {
	if len(idCard) != 18 {
		return time.Time{}, nil
	}
	
	birthDateStr := idCard[6:14]
	return time.Parse("20060102", birthDateStr)
}
