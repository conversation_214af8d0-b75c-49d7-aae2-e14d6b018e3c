package utils

import (
	"testing"
	"time"
)

func TestParseDurationString(t *testing.T) {
	// 设置一个固定的开始时间用于测试
	startTime := time.Date(2023, 5, 1, 12, 0, 0, 0, time.Local)

	// 测试用例
	tests := []struct {
		name        string
		durationStr string
		want        time.Time
		wantErr     bool
	}{
		{
			name:        "1年",
			durationStr: "1-0-0",
			want:        time.Date(2024, 5, 1, 12, 0, 0, 0, time.Local),
			wantErr:     false,
		},
		{
			name:        "1个月",
			durationStr: "0-1-0",
			want:        time.Date(2023, 6, 1, 12, 0, 0, 0, time.Local),
			wantErr:     false,
		},
		{
			name:        "7天",
			durationStr: "0-0-7",
			want:        time.Date(2023, 5, 8, 12, 0, 0, 0, time.Local),
			wantErr:     false,
		},
		{
			name:        "1年2个月3天",
			durationStr: "1-2-3",
			want:        time.Date(2024, 7, 4, 12, 0, 0, 0, time.Local),
			wantErr:     false,
		},
		{
			name:        "100年（长期会员）",
			durationStr: "100-0-0",
			want:        time.Date(2123, 5, 1, 12, 0, 0, 0, time.Local),
			wantErr:     false,
		},
		{
			name:        "无效格式",
			durationStr: "invalid",
			want:        time.Time{},
			wantErr:     true,
		},
		{
			name:        "格式错误",
			durationStr: "1-0",
			want:        time.Time{},
			wantErr:     true,
		},
		{
			name:        "非数字年份",
			durationStr: "a-0-0",
			want:        time.Time{},
			wantErr:     true,
		},
		{
			name:        "非数字月份",
			durationStr: "0-b-0",
			want:        time.Time{},
			wantErr:     true,
		},
		{
			name:        "非数字天数",
			durationStr: "0-0-c",
			want:        time.Time{},
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDurationString(startTime, tt.durationStr)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseDurationString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望有错误，不需要检查返回值
			if tt.wantErr {
				return
			}

			// 检查返回的时间是否符合预期
			if !got.Equal(tt.want) {
				t.Errorf("ParseDurationString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDurationDescription(t *testing.T) {
	tests := []struct {
		name        string
		durationStr string
		want        string
		wantErr     bool
	}{
		{
			name:        "1年",
			durationStr: "1-0-0",
			want:        "1年",
			wantErr:     false,
		},
		{
			name:        "1个月",
			durationStr: "0-1-0",
			want:        "1个月",
			wantErr:     false,
		},
		{
			name:        "7天",
			durationStr: "0-0-7",
			want:        "7天",
			wantErr:     false,
		},
		{
			name:        "1年2个月3天",
			durationStr: "1-2-3",
			want:        "1年2个月3天",
			wantErr:     false,
		},
		{
			name:        "100年（长期会员）",
			durationStr: "100-0-0",
			want:        "100年",
			wantErr:     false,
		},
		{
			name:        "无效格式",
			durationStr: "invalid",
			want:        "",
			wantErr:     true,
		},
		{
			name:        "零时长",
			durationStr: "0-0-0",
			want:        "0天",
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetDurationDescription(tt.durationStr)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDurationDescription() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 如果期望有错误，不需要检查返回值
			if tt.wantErr {
				return
			}

			// 检查返回的描述是否符合预期
			if got != tt.want {
				t.Errorf("GetDurationDescription() = %v, want %v", got, tt.want)
			}
		})
	}
}
