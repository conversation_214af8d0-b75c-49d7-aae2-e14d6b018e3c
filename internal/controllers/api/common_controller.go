package api

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"time"

	"recycle-server/config"
	"recycle-server/internal/response"
	"recycle-server/internal/services"
	"recycle-server/internal/vo"

	"github.com/gin-gonic/gin"
)

// CommonController 通用控制器
type CommonController struct {
	helpService               services.HelpService
	homeRecommendationService services.HomeRecommendationService
	productService            services.ProductService
	config                    *config.Config
}

// NewCommonController 创建通用控制器实例
func NewCommonController(helpService services.HelpService, homeRecommendationService services.HomeRecommendationService, productService services.ProductService, cfg *config.Config) *CommonController {
	return &CommonController{
		helpService:               helpService,
		homeRecommendationService: homeRecommendationService,
		productService:            productService,
		config:                    cfg,
	}
}

// SystemConfig 获取系统配置
// @Summary 获取系统配置
// @Description 获取系统配置信息，包括热门搜索、公告、首页推荐等
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.SystemConfigResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/system-config [get]
func (c *CommonController) SystemConfig(ctx *gin.Context) {
	// 获取首页推荐数据
	homeRecommendations, err := c.homeRecommendationService.GetActiveRecommendations()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 构建 hero 图完整URL
	heroImageURL := c.buildImageURL("public/system/666.jpg")

	// 创建系统配置响应
	config := vo.SystemConfigResponse{
		HotSearch: []string{
			"京东e卡",
			"携程",
			"微信立减金",
			"支付宝消费券",
			"大润发",
			"美通",
			"天虹",
			"万通金券",
			"天猫",
			"沃尔玛",
			"瑞祥",
			"美团",
		},
		Notice:              "【京顺回收】160余种卡券回收，如需了解更多问题，请点击咨询人工客服：09:00-24:00 24小时在线回收卡券，卡券提交后所有交易均可自动处理、24小时均可提现，1-3分钟内立即到账！",
		HomeRecommendations: homeRecommendations,
		IcpNumber:           "湘ICP备2023018441号-2X",
		HeroImageURL:        heroImageURL,
	}

	response.SuccessJSON(ctx, "获取系统配置成功", config)
}

// GetAllHelps 获取所有帮助信息
// @Summary 获取所有帮助信息
// @Description 获取所有帮助信息，按排序字段升序排列
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.HelpListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/helps [get]
func (c *CommonController) GetAllHelps(ctx *gin.Context) {
	// 调用服务层获取所有帮助信息
	result, err := c.helpService.GetAllHelps()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取帮助信息成功", result)
}

// GetAllBanks 获取所有银行列表
// @Summary 获取所有银行列表
// @Description 从系统配置文件中读取所有银行列表信息
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.BankListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/banks [get]
func (c *CommonController) GetAllBanks(ctx *gin.Context) {
	// 读取银行配置文件
	bankData, err := os.ReadFile("public/system/bank.json")
	if err != nil {
		response.ServerError(ctx, "读取银行配置文件失败")
		return
	}

	// 解析JSON数据
	var banks []string
	if err := json.Unmarshal(bankData, &banks); err != nil {
		response.ServerError(ctx, "解析银行配置文件失败")
		return
	}

	// 创建响应数据
	result := vo.BankListResponse{
		Banks: banks,
	}

	response.SuccessJSON(ctx, "获取银行列表成功", result)
}

// GetRecentOrders 获取最近订单
// @Summary 获取最近订单
// @Description 获取最近订单信息，包含面值、时间、消息、产品名称等
// @Tags API/通用接口
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.RecentOrderListResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/common/recent-orders [get]
func (c *CommonController) GetRecentOrders(ctx *gin.Context) {
	// 获取产品分类数据用于随机选择产品
	categories, err := c.productService.GetProductsWithCategories()
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 收集所有产品
	var allProducts []vo.ProductSummaryResponse
	for _, category := range categories {
		allProducts = append(allProducts, category.Children...)
	}

	// 如果没有产品，返回空列表
	if len(allProducts) == 0 {
		result := vo.RecentOrderListResponse{
			List: []vo.RecentOrderResponse{},
		}
		response.SuccessJSON(ctx, "获取最近订单成功", result)
		return
	}

	// Mock 姓名列表
	mockNames := []string{
		"陈**", "李**", "王**", "张**", "刘**", "赵**", "孙**", "周**", "吴**", "郑**",
		"冯**", "陈**", "褚**", "卫**", "蒋**", "沈**", "韩**", "杨**", "朱**", "秦**",
		"尤**", "许**", "何**", "吕**", "施**", "张**", "孔**", "曹**", "严**", "华**",
	}

	// Mock 面值列表
	mockFaceValues := []float64{100, 200, 300, 500, 1000, 1500, 2000, 3000, 5000}

	// 生成 Mock 数据
	var recentOrders []vo.RecentOrderResponse
	rand.Seed(time.Now().UnixNano())

	// 生成 10 条最近订单数据
	for i := 0; i < 10; i++ {
		// 随机选择产品
		randomProduct := allProducts[rand.Intn(len(allProducts))]

		// 随机选择面值
		randomFaceValue := mockFaceValues[rand.Intn(len(mockFaceValues))]

		// 随机选择姓名
		randomName := mockNames[rand.Intn(len(mockNames))]

		// 生成随机时间（最近24小时内）
		now := time.Now()
		randomMinutes := rand.Intn(24 * 60) // 24小时内的随机分钟数
		randomTime := now.Add(-time.Duration(randomMinutes) * time.Minute)

		// 构建消息
		msg := fmt.Sprintf("%s提交了%.0f%s", randomName, randomFaceValue, randomProduct.Name)

		recentOrder := vo.RecentOrderResponse{
			FaceValue:  randomFaceValue,
			CreateTime: randomTime.Format("2006-01-02 15:04:05"),
			Msg:        msg,
			PName:      randomProduct.Name,
		}

		recentOrders = append(recentOrders, recentOrder)
	}

	result := vo.RecentOrderListResponse{
		List: recentOrders,
	}

	response.SuccessJSON(ctx, "获取最近订单成功", result)
}

// buildImageURL 构建完整的图片URL
func (c *CommonController) buildImageURL(imageURL string) string {
	// 如果图片URL为空，返回空字符串
	if imageURL == "" {
		return ""
	}

	// 如果图片URL已经是完整的URL（包含http://或https://），直接返回
	if strings.HasPrefix(imageURL, "http://") || strings.HasPrefix(imageURL, "https://") {
		return imageURL
	}

	// 如果后端域名配置为空，直接返回原URL
	if c.config.BackendDomain == "" {
		return imageURL
	}

	// 确保后端域名不以/结尾，图片URL以/开头
	backendDomain := strings.TrimSuffix(c.config.BackendDomain, "/")
	if !strings.HasPrefix(imageURL, "/") {
		imageURL = "/" + imageURL
	}

	return backendDomain + imageURL
}
