package api

import (
	"recycle-server/internal/controllers/api"
	"recycle-server/internal/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterProductRoutes 注册产品相关路由
func RegisterProductRoutes(
	router *gin.RouterGroup,
	productController *api.ProductController,
) {
	// 产品路由组 - 不需要认证
	productGroup := router.Group("/products")
	{
		// 获取带分类分组的产品信息
		productGroup.GET("/categories", productController.GetProductsWithCategories)
		// 搜索产品（使用验证器和DTO）
		productGroup.GET("/search", middleware.WithValidation(productController.SearchProducts))
		// 获取所有热门产品
		productGroup.GET("/hot", productController.GetHotProducts)
		// 根据ID获取产品详情（使用验证器和DTO）
		productGroup.GET("/:id", middleware.WithValidation(productController.GetProductDetail))
	}
}
