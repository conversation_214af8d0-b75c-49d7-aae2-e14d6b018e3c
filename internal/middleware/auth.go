package middleware

import (
	"strings"

	"recycle-server/internal/exception"
	"recycle-server/internal/pkg"
	"recycle-server/internal/response"
	"recycle-server/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// JWTAuthMiddleware JWT认证中间件
func JWTAuthMiddleware(jwtService pkg.JWTService, userService services.UserService, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("请先登录"))
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			response.ThrowError(c, exception.ErrTokenInvalid.WithMessage("无效的认证头"))
			return
		}

		// 验证令牌
		token := parts[1]

		// 检查token是否在黑名单中
		exists, err := redisClient.Exists(c, "black:"+token).Result()
		if err != nil {
			pkg.Error("检查token黑名单失败", zap.Error(err))
			response.ThrowError(c, exception.ErrInternalServer)
			return
		}
		if exists == 1 {
			pkg.Warn("token已被加入黑名单")
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("登录已失效，请重新登录"))
			return
		}

		claims, err := jwtService.ParseToken(token)
		if err != nil {
			pkg.Warn("Token验证失败", zap.Error(err))
			response.ThrowError(c, exception.ErrTokenInvalid.WithMessage("无效的Token"))
			return
		}

		// 检查令牌是否过期
		if jwtService.IsExpired(claims) {
			pkg.Warn("Token已过期")
			response.ThrowError(c, exception.ErrTokenExpired)
			return
		}

		// 获取用户ID
		userID := jwtService.GetUserID(claims)

		// 查询数据库获取用户信息
		user, err := userService.GetUserByID(userID)
		if err != nil {
			pkg.Error("获取用户信息失败", zap.Uint("userId", userID), zap.Error(err))
			// 统一使用ErrUnauthorized处理用户不存在或状态异常的情况
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage(err.Error()))
			return
		}

		// 将用户ID和用户模型保存到上下文中
		c.Set("userId", userID)
		c.Set("user", user)
		c.Next()
	}
}
