package api

import (
	"strings"

	"recycle-server/config"
	"recycle-server/internal/dto"
	"recycle-server/internal/exception"
	"recycle-server/internal/models"
	"recycle-server/internal/pkg"
	"recycle-server/internal/response"
	"recycle-server/internal/services"
	"recycle-server/internal/vo"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

// UserController 用户控制器
type UserController struct {
	userService services.UserService
	jwtService  pkg.JWTService
	config      *config.Config
	redisClient *redis.Client
}

// NewUserController 创建用户控制器
func NewUserController(
	userService services.UserService,
	jwtService pkg.JWTService,
	cfg *config.Config,
	redisClient *redis.Client,
) *UserController {
	return &UserController{
		userService: userService,
		jwtService:  jwtService,
		config:      cfg,
		redisClient: redisClient,
	}
}

// GetUserInfo 获取当前登录用户信息
// @Summary 获取当前登录用户信息
// @Description 获取当前登录用户的详细信息
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo.UserResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "用户不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/info [get]
func (c *UserController) GetUserInfo(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	response.SuccessJSON(ctx, "获取成功", convertToUserResponse(user))
}

// Logout 退出登录
// @Summary 退出登录
// @Description 用户退出登录，清除登录状态
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse "退出成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Security ApiKeyAuth
// @Router /api/users/logout [post]
func (c *UserController) Logout(ctx *gin.Context) {
	// 从上下文中获取用户信息
	userObj, exists := ctx.Get("user")
	if !exists {
		pkg.Warn("用户未登录")
		response.ThrowError(ctx, exception.ErrUnauthorized)
		return
	}

	// 类型断言
	user, ok := userObj.(*models.User)
	if !ok {
		pkg.Error("用户信息类型错误")
		response.ThrowError(ctx, exception.ErrInternalServer)
		return
	}

	// 获取token
	authHeader := ctx.GetHeader("Authorization")
	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 调用服务层的Logout方法
	err := c.userService.Logout(ctx, user, token)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON[any](ctx, "退出成功", nil)
}

// UpdateUsername 修改用户名
// @Summary 修改用户名
// @Description 修改当前登录用户的用户名
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Param request body dto.UpdateUsernameRequest true "修改用户名请求"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/username [put]
func (c *UserController) UpdateUsername(ctx *gin.Context, req dto.UpdateUsernameRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层更新用户名
	err := c.userService.UpdateUsername(user.ID, req.Username)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "用户名修改成功")
}

// UpdateAvatar 修改用户头像
// @Summary 修改用户头像
// @Description 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
// @Tags API/用户管理
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/avatar [put]
func (c *UserController) UpdateAvatar(ctx *gin.Context, req dto.UpdateAvatarRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层处理文件上传和头像更新
	err := c.userService.UpdateAvatarWithFile(ctx, user.ID, req.File)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "头像修改成功")
}

// DeleteAccount 注销账户
// @Summary 注销账户
// @Description 软删除当前登录用户的账户，注销后无法恢复
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse "注销成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/delete [delete]
func (c *UserController) DeleteAccount(ctx *gin.Context) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层删除用户账户
	err := c.userService.DeleteUser(ctx, user.ID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	// 获取token并加入黑名单（注销后立即失效）
	authHeader := ctx.GetHeader("Authorization")
	token := strings.TrimPrefix(authHeader, "Bearer ")

	// 调用退出登录逻辑，将token加入黑名单
	_ = c.userService.Logout(ctx, user, token)

	response.SuccessNoDataJSON(ctx, "账户注销成功")
}

// UpdateTradePassword 修改交易密码
// @Summary 修改交易密码
// @Description 修改当前登录用户的交易密码，需要手机验证码验证
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Param request body dto.UpdateTradePasswordRequest true "修改交易密码请求"
// @Success 200 {object} vo.SuccessAPIResponse "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权或验证码错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/trade-password [put]
func (c *UserController) UpdateTradePassword(ctx *gin.Context, req dto.UpdateTradePasswordRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层修改交易密码
	err := c.userService.UpdateTradePassword(ctx, user.ID, req.TradePassword, req.Code)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "交易密码修改成功")
}

// RealNameAuth 实名认证
// @Summary 实名认证
// @Description 提交真实姓名和身份证号进行实名认证，认证成功后用户认证类型变更为普通认证
// @Tags API/用户管理
// @Accept json
// @Produce json
// @Param request body dto.RealNameAuthRequest true "实名认证请求"
// @Success 200 {object} vo.SuccessAPIResponse "认证成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误或已认证"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security ApiKeyAuth
// @Router /api/users/real-name-auth [post]
func (c *UserController) RealNameAuth(ctx *gin.Context, req dto.RealNameAuthRequest) {
	// 从上下文中获取用户信息（中间件已经完成认证）
	user := ctx.MustGet("user").(*models.User)

	// 调用服务层进行实名认证
	err := c.userService.RealNameAuth(user.ID, req.RealName, req.IDCard)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "实名认证成功")
}

// convertToUserResponse 将用户模型转换为响应VO
func convertToUserResponse(user *models.User) vo.UserResponse {
	return vo.UserResponse{
		ID:               user.ID,
		Username:         user.Username,
		Phone:            user.Phone,
		OpenID:           user.OpenID,
		Avatar:           user.Avatar,
		Balance:          user.Balance,
		FriendCount:      user.FriendCount,
		PromotionEarning: user.PromotionEarning,
		CreatedAt:        user.CreatedAt,
		UserType:         user.UserType,
		AuthType:         user.AuthType,
		RealName:         user.RealName,
		IDCard:           user.IDCard,
	}
}
