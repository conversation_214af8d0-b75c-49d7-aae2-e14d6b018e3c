package models

// HomeRecommendation 首页热门推荐模型
type HomeRecommendation struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	ImageURL    string `json:"image_url" gorm:"size:500;not null;default:'';comment:图片链接"`
	Title       string `json:"title" gorm:"size:200;not null;default:'';comment:文案标题"`
	JumpURL     string `json:"jump_url" gorm:"size:500;not null;default:'';comment:跳转链接"`
	Description string `json:"description" gorm:"type:text;comment:描述"`
	Sort        int    `json:"sort" gorm:"type:int;not null;default:0;index;comment:排序字段，数值越小越靠前"`
	IsActive    bool   `json:"is_active" gorm:"type:tinyint(1);not null;default:1;index;comment:是否启用 1:启用 0:禁用"`

	Base
}

// TableName 指定表名
func (HomeRecommendation) TableName() string {
	return "home_recommendation"
}
