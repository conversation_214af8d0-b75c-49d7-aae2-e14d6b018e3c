package pkg

import (
	"errors"
	"time"

	"recycle-server/config"

	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

// Claims 自定义JWT声明
type Claims struct {
	UserID uint `json:"user_id"`
	jwt.RegisteredClaims
}

// JWTService JWT服务接口
type JWTService interface {
	GenerateToken(userID uint) (string, error)
	ParseToken(tokenString string) (*Claims, error)
	GetUserID(claims *Claims) uint
	IsExpired(claims *Claims) bool
	GetExpirationTime(claims *Claims) time.Time
}

// jwtService JWT服务实现
type jwtService struct {
	config *config.Config
}

// NewJWTService 创建JWT服务
func NewJWTService(config *config.Config) JWTService {
	return &jwtService{
		config: config,
	}
}

// GenerateToken 生成JWT令牌
func (s *jwtService) GenerateToken(userID uint) (string, error) {
	claims := Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(s.config.JWT.TTL) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 创建Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名生成Token字符串
	tokenString, err := token.SignedString([]byte(s.config.JWT.Secret))
	if err != nil {
		Error("JWT Token生成失败", zap.Error(err))
		return "", err
	}

	return tokenString, nil
}

// ParseToken 解析JWT令牌
func (s *jwtService) ParseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名算法")
		}
		return []byte(s.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的Token")
}

// GetUserID 从Token中获取用户ID
func (s *jwtService) GetUserID(claims *Claims) uint {
	return claims.UserID
}

// IsExpired 检查Token是否已过期
func (s *jwtService) IsExpired(claims *Claims) bool {
	now := time.Now()
	if claims.ExpiresAt != nil {
		return now.After(claims.ExpiresAt.Time)
	}
	return false
}

// GetExpirationTime 获取Token的过期时间
func (s *jwtService) GetExpirationTime(claims *Claims) time.Time {
	if claims.ExpiresAt != nil {
		return claims.ExpiresAt.Time
	}
	return time.Time{}
}
